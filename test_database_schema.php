<?php
/**
 * اختبار مخطط قاعدة البيانات - ExRayan Platform
 * Database Schema Test
 */

header('Content-Type: text/html; charset=UTF-8');

$results = [
    'file_check' => false,
    'syntax_check' => false,
    'problematic_lines' => [],
    'fixed_issues' => [],
    'recommendations' => []
];

// فحص وجود ملف قاعدة البيانات
if (file_exists('database_schema.sql')) {
    $results['file_check'] = true;
    
    // قراءة محتوى الملف
    $content = file_get_contents('database_schema.sql');
    $lines = explode("\n", $content);
    
    $results['syntax_check'] = true;
    
    // فحص المشاكل الشائعة
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        
        // فحص حقول timestamp بدون قيم افتراضية
        if (preg_match('/`(\w+)`\s+timestamp\s+NOT\s+NULL(?!\s+DEFAULT)/i', $line, $matches)) {
            $results['problematic_lines'][] = [
                'line' => $lineNum + 1,
                'content' => $line,
                'issue' => "حقل timestamp مطلوب بدون قيمة افتراضية: {$matches[1]}",
                'severity' => 'error'
            ];
        }
        
        // فحص حقول datetime بدون قيم افتراضية
        if (preg_match('/`(\w+)`\s+datetime\s+NOT\s+NULL(?!\s+DEFAULT)/i', $line, $matches)) {
            $results['problematic_lines'][] = [
                'line' => $lineNum + 1,
                'content' => $line,
                'issue' => "حقل datetime مطلوب بدون قيمة افتراضية: {$matches[1]}",
                'severity' => 'error'
            ];
        }
        
        // فحص الإصلاحات المطبقة
        if (preg_match('/`end_date`\s+timestamp\s+NULL\s+DEFAULT\s+NULL/i', $line)) {
            $results['fixed_issues'][] = "تم إصلاح حقل end_date ليكون اختياري";
        }
        
        if (preg_match('/`start_date`\s+timestamp\s+NOT\s+NULL\s+DEFAULT\s+CURRENT_TIMESTAMP/i', $line)) {
            $results['fixed_issues'][] = "تم إصلاح حقل start_date ليحتوي على قيمة افتراضية";
        }
    }
    
    // التوصيات
    if (empty($results['problematic_lines'])) {
        $results['recommendations'][] = "مخطط قاعدة البيانات يبدو سليماً";
        $results['recommendations'][] = "يمكنك المتابعة مع التثبيت";
    } else {
        $results['recommendations'][] = "يوجد مشاكل في مخطط قاعدة البيانات";
        $results['recommendations'][] = "استخدم أداة إصلاح المخطط قبل التثبيت";
    }
    
} else {
    $results['recommendations'][] = "ملف database_schema.sql غير موجود";
    $results['recommendations'][] = "تأكد من تحميل جميع ملفات المشروع";
}

$totalIssues = count($results['problematic_lines']);
$totalFixes = count($results['fixed_issues']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مخطط قاعدة البيانات - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: #f5f7fa; padding: 20px; }
        .test-container { max-width: 900px; margin: 0 auto; }
        .test-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .code-line { background: #f8f9fa; border-left: 3px solid #dee2e6; padding: 10px; margin: 5px 0; font-family: monospace; font-size: 0.9rem; }
        .issue-error { border-left-color: #dc3545; }
        .issue-warning { border-left-color: #ffc107; }
        .issue-fixed { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-database text-primary"></i> اختبار مخطط قاعدة البيانات</h1>
            <p class="text-muted">فحص ملف database_schema.sql للتأكد من سلامته</p>
        </div>
        
        <!-- ملخص النتائج -->
        <div class="test-card">
            <h3 class="text-center mb-4">ملخص النتائج</h3>
            
            <div class="row text-center mb-4">
                <div class="col-md-3">
                    <div class="<?= $results['file_check'] ? 'status-pass' : 'status-fail' ?>">
                        <i class="fas fa-<?= $results['file_check'] ? 'check' : 'times' ?>-circle fa-2x"></i>
                        <h5>ملف قاعدة البيانات</h5>
                        <small><?= $results['file_check'] ? 'موجود' : 'غير موجود' ?></small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="<?= $results['syntax_check'] ? 'status-pass' : 'status-fail' ?>">
                        <i class="fas fa-<?= $results['syntax_check'] ? 'check' : 'times' ?>-circle fa-2x"></i>
                        <h5>فحص الصيغة</h5>
                        <small><?= $results['syntax_check'] ? 'سليم' : 'يحتوي أخطاء' ?></small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="<?= $totalIssues == 0 ? 'status-pass' : 'status-fail' ?>">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                        <h5><?= $totalIssues ?></h5>
                        <small>مشاكل مكتشفة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-pass">
                        <i class="fas fa-wrench fa-2x"></i>
                        <h5><?= $totalFixes ?></h5>
                        <small>إصلاحات مطبقة</small>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if (!empty($results['fixed_issues'])): ?>
        <!-- الإصلاحات المطبقة -->
        <div class="test-card">
            <h4><i class="fas fa-check-circle text-success me-2"></i>الإصلاحات المطبقة</h4>
            <?php foreach ($results['fixed_issues'] as $fix): ?>
            <div class="code-line issue-fixed">
                <i class="fas fa-check me-2"></i>
                <?= htmlspecialchars($fix) ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($results['problematic_lines'])): ?>
        <!-- المشاكل المكتشفة -->
        <div class="test-card">
            <h4><i class="fas fa-exclamation-triangle text-danger me-2"></i>المشاكل المكتشفة</h4>
            <?php foreach ($results['problematic_lines'] as $problem): ?>
            <div class="code-line issue-<?= $problem['severity'] ?>">
                <strong>السطر <?= $problem['line'] ?>:</strong> <?= htmlspecialchars($problem['issue']) ?><br>
                <code><?= htmlspecialchars($problem['content']) ?></code>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <!-- التوصيات -->
        <div class="test-card">
            <h4><i class="fas fa-lightbulb text-info me-2"></i>التوصيات</h4>
            <?php foreach ($results['recommendations'] as $recommendation): ?>
            <div class="alert alert-<?= $totalIssues == 0 ? 'success' : 'warning' ?> mb-2">
                <i class="fas fa-<?= $totalIssues == 0 ? 'check' : 'exclamation-triangle' ?> me-2"></i>
                <?= htmlspecialchars($recommendation) ?>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- الإجراءات -->
        <div class="test-card text-center">
            <h4><i class="fas fa-tools me-2"></i>الإجراءات المتاحة</h4>
            
            <?php if ($totalIssues > 0): ?>
            <div class="alert alert-warning">
                <strong>يوجد مشاكل في مخطط قاعدة البيانات!</strong><br>
                يُنصح بإصلاح هذه المشاكل قبل التثبيت.
            </div>
            
            <a href="fix_database_schema.php" class="btn btn-warning me-2">
                <i class="fas fa-wrench me-2"></i>
                إصلاح مخطط قاعدة البيانات
            </a>
            <?php else: ?>
            <div class="alert alert-success">
                <strong>مخطط قاعدة البيانات سليم!</strong><br>
                يمكنك المتابعة مع التثبيت بأمان.
            </div>
            
            <a href="install.php" class="btn btn-success me-2">
                <i class="fas fa-download me-2"></i>
                بدء التثبيت
            </a>
            <?php endif; ?>
            
            <a href="welcome.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <button class="btn btn-info" onclick="location.reload()">
                <i class="fas fa-redo me-2"></i>
                إعادة الفحص
            </button>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="test-card">
            <h6>معلومات الفحص:</h6>
            <ul class="small text-muted mb-0">
                <li>تم فحص ملف database_schema.sql للبحث عن مشاكل شائعة</li>
                <li>التركيز على حقول timestamp و datetime بدون قيم افتراضية</li>
                <li>فحص الإصلاحات المطبقة مسبقاً</li>
                <li>تقديم توصيات للخطوات التالية</li>
            </ul>
        </div>
    </div>
</body>
</html>
