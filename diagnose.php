<?php
/**
 * أداة التشخيص الشاملة - ExRayan Platform
 * Comprehensive Diagnostic Tool
 */

header('Content-Type: text/html; charset=UTF-8');

// تحديد مستوى الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

$diagnostics = [
    'system' => [],
    'php' => [],
    'database' => [],
    'files' => [],
    'permissions' => [],
    'ajax' => []
];

// فحص النظام
$diagnostics['system']['os'] = PHP_OS;
$diagnostics['system']['server'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$diagnostics['system']['document_root'] = $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown';
$diagnostics['system']['script_name'] = $_SERVER['SCRIPT_NAME'] ?? 'Unknown';
$diagnostics['system']['request_method'] = $_SERVER['REQUEST_METHOD'] ?? 'Unknown';

// فحص PHP
$diagnostics['php']['version'] = PHP_VERSION;
$diagnostics['php']['version_ok'] = version_compare(PHP_VERSION, '7.4.0', '>=');
$diagnostics['php']['memory_limit'] = ini_get('memory_limit');
$diagnostics['php']['max_execution_time'] = ini_get('max_execution_time');
$diagnostics['php']['upload_max_filesize'] = ini_get('upload_max_filesize');
$diagnostics['php']['post_max_size'] = ini_get('post_max_size');

// فحص الإضافات
$required_extensions = ['mysqli', 'pdo_mysql', 'json', 'openssl', 'mbstring', 'fileinfo'];
foreach ($required_extensions as $ext) {
    $diagnostics['php']['extensions'][$ext] = extension_loaded($ext);
}

// فحص الملفات
$required_files = [
    '.env' => file_exists('.env'),
    'database_schema.sql' => file_exists('database_schema.sql'),
    'install.php' => file_exists('install.php'),
    '.installed' => file_exists('.installed'),
    'includes/Database.php' => file_exists('includes/Database.php'),
    'includes/Security.php' => file_exists('includes/Security.php'),
    'controllers/home.php' => file_exists('controllers/home.php')
];

foreach ($required_files as $file => $exists) {
    $diagnostics['files'][$file] = [
        'exists' => $exists,
        'readable' => $exists ? is_readable($file) : false,
        'size' => $exists ? filesize($file) : 0
    ];
}

// فحص المجلدات والصلاحيات
$required_dirs = ['uploads', 'logs', 'backups', 'admin', 'controllers', 'includes', 'views'];
foreach ($required_dirs as $dir) {
    $exists = is_dir($dir);
    $diagnostics['permissions'][$dir] = [
        'exists' => $exists,
        'readable' => $exists ? is_readable($dir) : false,
        'writable' => $exists ? is_writable($dir) : false,
        'permissions' => $exists ? substr(sprintf('%o', fileperms($dir)), -4) : 'N/A'
    ];
}

// فحص قاعدة البيانات
try {
    // تحميل متغيرات البيئة
    if (file_exists('.env')) {
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value, '"\'');
            }
        }
    }
    
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '000';
    
    $diagnostics['database']['config'] = [
        'host' => $host,
        'database' => $dbname,
        'username' => $username,
        'password' => str_repeat('*', strlen($password))
    ];
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $diagnostics['database']['connection'] = true;
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([$dbname]);
    $diagnostics['database']['database_exists'] = $stmt->rowCount() > 0;
    
    if ($diagnostics['database']['database_exists']) {
        // الاتصال بقاعدة البيانات المحددة
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // فحص الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $diagnostics['database']['tables'] = $tables;
        $diagnostics['database']['tables_count'] = count($tables);
        
        // فحص جدول المستخدمين
        if (in_array('users', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            $diagnostics['database']['users_count'] = $result['count'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE classification = 'admin'");
            $result = $stmt->fetch();
            $diagnostics['database']['admin_count'] = $result['count'];
        }
    }
    
} catch (Exception $e) {
    $diagnostics['database']['connection'] = false;
    $diagnostics['database']['error'] = $e->getMessage();
}

// فحص AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax_test'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'AJAX يعمل بشكل صحيح']);
    exit;
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: #f5f7fa; padding: 20px; }
        .diagnostic-container { max-width: 1200px; margin: 0 auto; }
        .section-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-ok { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .info-table { font-size: 0.9rem; }
        .info-table td { padding: 5px 10px; border-bottom: 1px solid #eee; }
        .section-title { color: #667eea; font-weight: 600; margin-bottom: 15px; }
        .btn-test { background: #667eea; border: none; color: white; padding: 8px 15px; border-radius: 5px; margin: 5px; }
        .btn-test:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-stethoscope text-primary"></i> تشخيص النظام الشامل</h1>
            <p class="text-muted">ExRayan Platform - System Diagnostics</p>
        </div>
        
        <!-- معلومات النظام -->
        <div class="section-card">
            <h3 class="section-title"><i class="fas fa-server"></i> معلومات النظام</h3>
            <table class="table info-table">
                <?php foreach ($diagnostics['system'] as $key => $value): ?>
                <tr>
                    <td><strong><?= ucfirst($key) ?>:</strong></td>
                    <td><?= htmlspecialchars($value) ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <!-- معلومات PHP -->
        <div class="section-card">
            <h3 class="section-title"><i class="fab fa-php"></i> معلومات PHP</h3>
            <table class="table info-table">
                <tr>
                    <td><strong>PHP Version:</strong></td>
                    <td>
                        <?= $diagnostics['php']['version'] ?>
                        <span class="<?= $diagnostics['php']['version_ok'] ? 'status-ok' : 'status-error' ?>">
                            <i class="fas fa-<?= $diagnostics['php']['version_ok'] ? 'check' : 'times' ?>"></i>
                        </span>
                    </td>
                </tr>
                <tr><td><strong>Memory Limit:</strong></td><td><?= $diagnostics['php']['memory_limit'] ?></td></tr>
                <tr><td><strong>Max Execution Time:</strong></td><td><?= $diagnostics['php']['max_execution_time'] ?> seconds</td></tr>
                <tr><td><strong>Upload Max Filesize:</strong></td><td><?= $diagnostics['php']['upload_max_filesize'] ?></td></tr>
                <tr><td><strong>Post Max Size:</strong></td><td><?= $diagnostics['php']['post_max_size'] ?></td></tr>
            </table>
            
            <h5>الإضافات المطلوبة:</h5>
            <div class="row">
                <?php foreach ($diagnostics['php']['extensions'] as $ext => $loaded): ?>
                <div class="col-md-4 col-sm-6">
                    <span class="<?= $loaded ? 'status-ok' : 'status-error' ?>">
                        <i class="fas fa-<?= $loaded ? 'check' : 'times' ?>"></i>
                        <?= $ext ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- الملفات -->
        <div class="section-card">
            <h3 class="section-title"><i class="fas fa-file"></i> الملفات المطلوبة</h3>
            <table class="table info-table">
                <?php foreach ($diagnostics['files'] as $file => $info): ?>
                <tr>
                    <td><strong><?= $file ?>:</strong></td>
                    <td>
                        <span class="<?= $info['exists'] ? 'status-ok' : 'status-error' ?>">
                            <i class="fas fa-<?= $info['exists'] ? 'check' : 'times' ?>"></i>
                            <?= $info['exists'] ? 'موجود' : 'غير موجود' ?>
                        </span>
                        <?php if ($info['exists']): ?>
                            (<?= number_format($info['size']) ?> bytes)
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <!-- الصلاحيات -->
        <div class="section-card">
            <h3 class="section-title"><i class="fas fa-lock"></i> المجلدات والصلاحيات</h3>
            <table class="table info-table">
                <?php foreach ($diagnostics['permissions'] as $dir => $info): ?>
                <tr>
                    <td><strong><?= $dir ?>:</strong></td>
                    <td>
                        <span class="<?= $info['exists'] ? 'status-ok' : 'status-error' ?>">
                            <i class="fas fa-<?= $info['exists'] ? 'check' : 'times' ?>"></i>
                            <?= $info['exists'] ? 'موجود' : 'غير موجود' ?>
                        </span>
                        <?php if ($info['exists']): ?>
                            | 
                            <span class="<?= $info['readable'] ? 'status-ok' : 'status-error' ?>">
                                قراءة: <?= $info['readable'] ? 'نعم' : 'لا' ?>
                            </span>
                            | 
                            <span class="<?= $info['writable'] ? 'status-ok' : 'status-warning' ?>">
                                كتابة: <?= $info['writable'] ? 'نعم' : 'لا' ?>
                            </span>
                            (<?= $info['permissions'] ?>)
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <!-- قاعدة البيانات -->
        <div class="section-card">
            <h3 class="section-title"><i class="fas fa-database"></i> قاعدة البيانات</h3>
            
            <?php if (isset($diagnostics['database']['config'])): ?>
            <h5>إعدادات الاتصال:</h5>
            <table class="table info-table">
                <?php foreach ($diagnostics['database']['config'] as $key => $value): ?>
                <tr>
                    <td><strong><?= ucfirst($key) ?>:</strong></td>
                    <td><?= htmlspecialchars($value) ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
            <?php endif; ?>
            
            <h5>حالة الاتصال:</h5>
            <p>
                <span class="<?= $diagnostics['database']['connection'] ? 'status-ok' : 'status-error' ?>">
                    <i class="fas fa-<?= $diagnostics['database']['connection'] ? 'check' : 'times' ?>"></i>
                    <?= $diagnostics['database']['connection'] ? 'متصل' : 'غير متصل' ?>
                </span>
                
                <?php if (!$diagnostics['database']['connection'] && isset($diagnostics['database']['error'])): ?>
                    <br><small class="text-danger">الخطأ: <?= htmlspecialchars($diagnostics['database']['error']) ?></small>
                <?php endif; ?>
            </p>
            
            <?php if ($diagnostics['database']['connection']): ?>
            <h5>معلومات قاعدة البيانات:</h5>
            <ul>
                <li>قاعدة البيانات موجودة: 
                    <span class="<?= $diagnostics['database']['database_exists'] ? 'status-ok' : 'status-error' ?>">
                        <?= $diagnostics['database']['database_exists'] ? 'نعم' : 'لا' ?>
                    </span>
                </li>
                <?php if (isset($diagnostics['database']['tables_count'])): ?>
                <li>عدد الجداول: <?= $diagnostics['database']['tables_count'] ?></li>
                <?php endif; ?>
                <?php if (isset($diagnostics['database']['users_count'])): ?>
                <li>عدد المستخدمين: <?= $diagnostics['database']['users_count'] ?></li>
                <li>عدد المديرين: <?= $diagnostics['database']['admin_count'] ?></li>
                <?php endif; ?>
            </ul>
            <?php endif; ?>
        </div>
        
        <!-- اختبار AJAX -->
        <div class="section-card">
            <h3 class="section-title"><i class="fas fa-flask"></i> اختبار AJAX</h3>
            <button class="btn btn-test" onclick="testAjax()">اختبار AJAX</button>
            <div id="ajaxResult" class="mt-3"></div>
        </div>
        
        <!-- الإجراءات -->
        <div class="section-card text-center">
            <h3 class="section-title"><i class="fas fa-tools"></i> الإجراءات</h3>
            <a href="welcome.php" class="btn btn-primary">الصفحة الرئيسية</a>
            <a href="install.php" class="btn btn-success">التثبيت</a>
            <a href="test_connection.php" class="btn btn-info">اختبار قاعدة البيانات</a>
            <a href="test_ajax.php" class="btn btn-warning">اختبار AJAX المفصل</a>
            <button class="btn btn-secondary" onclick="location.reload()">إعادة الفحص</button>
        </div>
    </div>
    
    <script>
        function testAjax() {
            const resultDiv = document.getElementById('ajaxResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار AJAX...';
            
            const formData = new FormData();
            formData.append('ajax_test', '1');
            
            fetch('diagnose.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML = `<div class="alert alert-success">✓ ${data.message}</div>`;
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-danger">✗ خطأ في تحليل JSON: ${e.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">✗ خطأ في AJAX: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
