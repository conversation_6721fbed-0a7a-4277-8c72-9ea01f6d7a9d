<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في ExRayan - منصة الإعلانات المبوبة</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .welcome-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .welcome-card {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .welcome-title {
            font-size: 3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .welcome-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .setup-steps {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            text-align: right;
        }
        
        .setup-steps h4 {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-left: 15px;
            flex-shrink: 0;
        }
        
        .step-text {
            flex: 1;
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }
        
        .btn-action {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        }
        
        .status-check {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: 600;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .welcome-card {
                padding: 40px 20px;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn-action {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-card">
            <div class="logo">
                <i class="fas fa-store"></i>
            </div>
            
            <h1 class="welcome-title">مرحباً بك في ExRayan</h1>
            <p class="welcome-subtitle">
                منصة الإعلانات المبوبة الأكثر تطوراً في الكويت<br>
                نظام شامل لإدارة الإعلانات والمتاجر والمزادات
            </p>
            
            <!-- فحص الحالة -->
            <div class="status-check">
                <h5 class="text-center mb-3">
                    <i class="fas fa-check-circle me-2"></i>
                    فحص حالة النظام
                </h5>
                
                <?php
                $checks = [
                    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                    'MySQL Extension' => extension_loaded('mysqli') || extension_loaded('pdo_mysql'),
                    'JSON Extension' => extension_loaded('json'),
                    'OpenSSL Extension' => extension_loaded('openssl'),
                    'Uploads Directory' => is_dir(__DIR__ . '/uploads') || mkdir(__DIR__ . '/uploads', 0755, true),
                    'Logs Directory' => is_dir(__DIR__ . '/logs') || mkdir(__DIR__ . '/logs', 0755, true),
                ];
                
                $allPassed = true;
                foreach ($checks as $check => $status) {
                    $statusClass = $status ? 'status-ok' : 'status-error';
                    $statusIcon = $status ? 'fas fa-check' : 'fas fa-times';
                    $statusText = $status ? 'متوفر' : 'غير متوفر';
                    if (!$status) $allPassed = false;
                    
                    echo "<div class='status-item'>";
                    echo "<span>{$check}</span>";
                    echo "<span class='{$statusClass}'><i class='{$statusIcon} me-1'></i>{$statusText}</span>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <!-- خطوات الإعداد -->
            <div class="setup-steps">
                <h4>خطوات الإعداد السريع</h4>
                
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-text">
                        <strong>إنشاء قاعدة البيانات:</strong>
                        قم بإنشاء قاعدة بيانات باسم "exrayan" من phpMyAdmin
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-text">
                        <strong>تشغيل التثبيت:</strong>
                        استخدم ملف التثبيت التفاعلي لإعداد النظام
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-text">
                        <strong>إنشاء حساب مدير:</strong>
                        قم بإنشاء حساب المدير للوصول للوحة التحكم
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-text">
                        <strong>البدء في الاستخدام:</strong>
                        ابدأ في إضافة المحتوى وتخصيص الموقع
                    </div>
                </div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <a href="install.php" class="btn-action">
                    <i class="fas fa-download"></i>
                    بدء التثبيت
                </a>
                
                <a href="test_connection.php" class="btn-action btn-secondary">
                    <i class="fas fa-database"></i>
                    اختبار قاعدة البيانات
                </a>
                
                <a href="create_admin.php" class="btn-action btn-success">
                    <i class="fas fa-user-shield"></i>
                    إنشاء مدير
                </a>

                <a href="status.php" class="btn-action btn-secondary" target="_blank">
                    <i class="fas fa-heartbeat"></i>
                    فحص حالة النظام
                </a>

                <a href="test_ajax.php" class="btn-action btn-secondary" target="_blank">
                    <i class="fas fa-flask"></i>
                    اختبار AJAX
                </a>

                <a href="diagnose.php" class="btn-action btn-secondary" target="_blank">
                    <i class="fas fa-stethoscope"></i>
                    تشخيص شامل
                </a>

                <a href="reset_database.php" class="btn-action" style="background: #dc3545;" target="_blank">
                    <i class="fas fa-redo"></i>
                    إعادة تعيين قاعدة البيانات
                </a>

                <a href="fix_admin_user.php" class="btn-action" style="background: #28a745;" target="_blank">
                    <i class="fas fa-user-shield"></i>
                    إصلاح صلاحيات المدير
                </a>

                <a href="test_system.php" class="btn-action" style="background: #17a2b8;" target="_blank">
                    <i class="fas fa-vial"></i>
                    اختبار النظام الشامل
                </a>

                <a href="fix_database_schema.php" class="btn-action" style="background: #ffc107; color: #212529;" target="_blank">
                    <i class="fas fa-database"></i>
                    إصلاح مخطط قاعدة البيانات
                </a>

                <a href="test_database_schema.php" class="btn-action" style="background: #6f42c1;" target="_blank">
                    <i class="fas fa-search"></i>
                    اختبار مخطط قاعدة البيانات
                </a>
            </div>
            
            <!-- المميزات -->
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <div class="feature-title">إدارة الإعلانات</div>
                    <div class="feature-desc">نظام شامل لإدارة الإعلانات المبوبة</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="feature-title">المتاجر الإلكترونية</div>
                    <div class="feature-desc">منصة متكاملة للمتاجر والبائعين</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-gavel"></i>
                    </div>
                    <div class="feature-title">نظام المزادات</div>
                    <div class="feature-desc">مزادات تفاعلية في الوقت الفعلي</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-title">أمان متقدم</div>
                    <div class="feature-desc">حماية شاملة وتشفير قوي</div>
                </div>
            </div>
            
            <div class="mt-4">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    ExRayan Platform v1.0 - تم التطوير بواسطة فريق ExRayan
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك البطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
            
            // فحص حالة التثبيت
            checkInstallationStatus();

            // فحص دوري لحالة النظام
            setInterval(checkInstallationStatus, 30000);
        });

        function checkInstallationStatus() {
            fetch('check_installation.php')
            .then(response => response.json())
            .then(data => {
                updateInstallationStatus(data);
            })
            .catch(error => {
                console.error('Error checking installation status:', error);
            });
        }

        function updateInstallationStatus(status) {
            const statusCheck = document.querySelector('.status-check');
            if (!statusCheck) return;

            // إضافة معلومات حالة التثبيت
            let installationInfo = statusCheck.querySelector('.installation-info');
            if (!installationInfo) {
                installationInfo = document.createElement('div');
                installationInfo.className = 'installation-info mt-3 p-3 rounded';
                statusCheck.appendChild(installationInfo);
            }

            let statusClass = 'bg-light border';
            let statusIcon = 'fas fa-info-circle';
            let statusColor = 'text-info';

            switch (status.overall_status) {
                case 'ready':
                    statusClass = 'bg-success bg-opacity-10 border-success';
                    statusIcon = 'fas fa-check-circle';
                    statusColor = 'text-success';
                    break;
                case 'needs_admin':
                    statusClass = 'bg-warning bg-opacity-10 border-warning';
                    statusIcon = 'fas fa-exclamation-triangle';
                    statusColor = 'text-warning';
                    break;
                case 'needs_installation':
                    statusClass = 'bg-info bg-opacity-10 border-info';
                    statusIcon = 'fas fa-download';
                    statusColor = 'text-info';
                    break;
                case 'needs_database':
                    statusClass = 'bg-danger bg-opacity-10 border-danger';
                    statusIcon = 'fas fa-database';
                    statusColor = 'text-danger';
                    break;
                default:
                    statusClass = 'bg-secondary bg-opacity-10 border-secondary';
                    statusIcon = 'fas fa-question-circle';
                    statusColor = 'text-secondary';
            }

            installationInfo.className = `installation-info mt-3 p-3 rounded ${statusClass}`;
            installationInfo.innerHTML = `
                <h6 class="${statusColor}">
                    <i class="${statusIcon} me-2"></i>
                    حالة التثبيت
                </h6>
                <p class="mb-2"><strong>${status.message}</strong></p>
                ${status.recommendations.map(rec => `<small class="d-block">• ${rec}</small>`).join('')}
                ${status.database_connected ? `<small class="d-block mt-2 text-muted">الجداول: ${status.tables_count || 0} | المستخدمين: ${status.total_users || 0}</small>` : ''}
            `;
        }
    </script>
</body>
</html>
