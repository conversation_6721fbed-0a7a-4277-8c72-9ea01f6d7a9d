<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في ExRayan</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .welcome-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .welcome-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }
        
        .welcome-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .action-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            color: #333;
            text-decoration: none;
        }
        
        .action-card.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .action-card.primary:hover {
            color: white;
        }
        
        .action-card.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .action-card.success:hover {
            color: white;
        }
        
        .action-card.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        
        .action-card.warning:hover {
            color: #212529;
        }
        
        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .action-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .status-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .footer-links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .welcome-container {
                padding: 20px;
                margin: 10px;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .action-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">
            <i class="fas fa-store"></i>
        </div>
        
        <h1 class="welcome-title">مرحباً بك في ExRayan</h1>
        <p class="welcome-subtitle">منصة الإعلانات المبوبة الأكثر تطوراً في الكويت</p>
        
        <div class="status-info">
            <h5><i class="fas fa-info-circle me-2"></i>حالة المشروع</h5>
            <p class="mb-0">
                <?php if (file_exists('.installed')): ?>
                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>المشروع مثبت ومُعد</span>
                <?php else: ?>
                    <span class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>المشروع يحتاج للإعداد</span>
                <?php endif; ?>
            </p>
        </div>
        
        <div class="action-grid">
            <?php if (!file_exists('.installed')): ?>
            <a href="setup.php" class="action-card success">
                <div class="action-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="action-title">إعداد سريع</div>
                <div class="action-desc">إعداد تلقائي وسريع للمشروع</div>
            </a>
            
            <a href="install.php" class="action-card warning">
                <div class="action-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="action-title">التثبيت التفاعلي</div>
                <div class="action-desc">تثبيت خطوة بخطوة مع خيارات متقدمة</div>
            </a>
            <?php else: ?>
            <a href="/" class="action-card primary">
                <div class="action-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="action-title">الصفحة الرئيسية</div>
                <div class="action-desc">انتقل للموقع الرئيسي</div>
            </a>
            
            <a href="admin/" class="action-card success">
                <div class="action-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="action-title">لوحة التحكم</div>
                <div class="action-desc">إدارة المحتوى والإعدادات</div>
            </a>
            <?php endif; ?>
            
            <a href="test_connection.php" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="action-title">اختبار قاعدة البيانات</div>
                <div class="action-desc">فحص الاتصال بقاعدة البيانات</div>
            </a>
            
            <a href="create_admin.php" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="action-title">إنشاء مدير</div>
                <div class="action-desc">إنشاء حساب مدير جديد</div>
            </a>
        </div>
        
        <div class="footer-links">
            <a href="README.md" target="_blank">
                <i class="fas fa-book me-1"></i>
                دليل المستخدم
            </a>
            <a href="INSTALLATION.md" target="_blank">
                <i class="fas fa-download me-1"></i>
                دليل التثبيت
            </a>
            <a href="TROUBLESHOOTING.md" target="_blank">
                <i class="fas fa-question-circle me-1"></i>
                حل المشاكل
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                ExRayan Platform v1.0 - تم التطوير بواسطة فريق ExRayan
            </small>
        </div>
    </div>
    
    <script>
        // فحص حالة المشروع كل 30 ثانية
        setInterval(() => {
            fetch('check_installation.php')
            .then(response => response.json())
            .then(data => {
                // يمكن إضافة تحديثات ديناميكية هنا
            })
            .catch(error => {
                // تجاهل الأخطاء
            });
        }, 30000);
    </script>
</body>
</html>
