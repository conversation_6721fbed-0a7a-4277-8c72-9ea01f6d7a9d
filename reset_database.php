<?php
/**
 * إعادة تعيين قاعدة البيانات - ExRayan Platform
 * Database Reset Tool
 */

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'reset_database') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            // الاتصال بالخادم (بدون تحديد قاعدة البيانات)
            $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // حذف قاعدة البيانات إذا كانت موجودة
            $pdo->exec("DROP DATABASE IF EXISTS `$dbname`");
            
            // إنشاء قاعدة البيانات من جديد
            $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // حذف ملف التثبيت
            if (file_exists('.installed')) {
                unlink('.installed');
            }
            
            echo json_encode([
                'success' => true, 
                'message' => 'تم إعادة تعيين قاعدة البيانات بنجاح'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false, 
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    if ($action === 'drop_tables') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // تعطيل فحص المفاتيح الخارجية
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // جلب جميع الجداول
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $dropped = 0;
            foreach ($tables as $table) {
                $pdo->exec("DROP TABLE IF EXISTS `$table`");
                $dropped++;
            }
            
            // إعادة تفعيل فحص المفاتيح الخارجية
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            // حذف ملف التثبيت
            if (file_exists('.installed')) {
                unlink('.installed');
            }
            
            echo json_encode([
                'success' => true, 
                'message' => "تم حذف $dropped جدول بنجاح"
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false, 
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين قاعدة البيانات - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh; padding: 20px; }
        .reset-container { background: white; border-radius: 15px; padding: 40px; max-width: 600px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .warning-icon { font-size: 4rem; color: #dc3545; margin-bottom: 20px; }
        .btn-danger-custom { background: #dc3545; border: none; color: white; padding: 12px 25px; border-radius: 8px; font-weight: 600; margin: 5px; }
        .btn-danger-custom:hover { background: #c82333; }
        .btn-warning-custom { background: #ffc107; border: none; color: #212529; padding: 12px 25px; border-radius: 8px; font-weight: 600; margin: 5px; }
        .btn-warning-custom:hover { background: #e0a800; }
        .alert { border-radius: 8px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="text-center">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1 class="text-danger mb-4">إعادة تعيين قاعدة البيانات</h1>
            <p class="lead text-muted mb-4">
                هذه الأداة ستقوم بحذف جميع البيانات الموجودة في قاعدة البيانات.<br>
                <strong class="text-danger">تأكد من عمل نسخة احتياطية قبل المتابعة!</strong>
            </p>
        </div>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم:</h5>
            <ul class="mb-0">
                <li>سيتم حذف جميع البيانات نهائياً</li>
                <li>سيتم حذف جميع المستخدمين والإعلانات</li>
                <li>لا يمكن التراجع عن هذا الإجراء</li>
                <li>ستحتاج لإعادة التثبيت من البداية</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title text-danger">
                            <i class="fas fa-database me-2"></i>
                            إعادة تعيين كاملة
                        </h5>
                        <p class="card-text">
                            حذف قاعدة البيانات بالكامل وإنشاؤها من جديد
                        </p>
                        <button class="btn btn-danger-custom" onclick="resetDatabase()">
                            <i class="fas fa-trash-alt me-2"></i>
                            إعادة تعيين كاملة
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title text-warning">
                            <i class="fas fa-table me-2"></i>
                            حذف الجداول فقط
                        </h5>
                        <p class="card-text">
                            حذف جميع الجداول مع الاحتفاظ بقاعدة البيانات
                        </p>
                        <button class="btn btn-warning-custom" onclick="dropTables()">
                            <i class="fas fa-eraser me-2"></i>
                            حذف الجداول
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="result" class="mt-4"></div>
        
        <div class="text-center mt-4">
            <a href="welcome.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <a href="install.php" class="btn btn-primary">
                <i class="fas fa-download me-2"></i>
                بدء التثبيت
            </a>
        </div>
        
        <div class="mt-4">
            <h6>معلومات قاعدة البيانات الحالية:</h6>
            <?php
            try {
                if (file_exists('.env')) {
                    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                    foreach ($lines as $line) {
                        if (strpos($line, '#') === 0) continue;
                        if (strpos($line, '=') !== false) {
                            list($key, $value) = explode('=', $line, 2);
                            $_ENV[trim($key)] = trim($value, '"\'');
                        }
                    }
                }
                
                $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
                $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
                $username = $_ENV['DB_USERNAME'] ?? 'root';
                
                echo "<small class='text-muted'>";
                echo "الخادم: $host<br>";
                echo "قاعدة البيانات: $dbname<br>";
                echo "المستخدم: $username<br>";
                
                // محاولة الاتصال وعرض عدد الجداول
                try {
                    $password = $_ENV['DB_PASSWORD'] ?? '000';
                    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                    $stmt = $pdo->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    echo "عدد الجداول: " . count($tables);
                } catch (Exception $e) {
                    echo "حالة الاتصال: غير متصل";
                }
                
                echo "</small>";
            } catch (Exception $e) {
                echo "<small class='text-danger'>خطأ في قراءة الإعدادات</small>";
            }
            ?>
        </div>
    </div>
    
    <script>
        function resetDatabase() {
            if (!confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات بالكامل؟\n\nسيتم حذف جميع البيانات نهائياً!')) {
                return;
            }
            
            if (!confirm('تأكيد أخير: هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }
            
            executeAction('reset_database', 'إعادة تعيين قاعدة البيانات');
        }
        
        function dropTables() {
            if (!confirm('هل أنت متأكد من حذف جميع الجداول؟\n\nسيتم حذف جميع البيانات!')) {
                return;
            }
            
            executeAction('drop_tables', 'حذف الجداول');
        }
        
        function executeAction(action, actionName) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري ${actionName}...</div>`;
            
            const formData = new FormData();
            formData.append('action', action);
            
            fetch('reset_database.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                ${data.message}
                                <br><br>
                                <a href="install.php" class="btn btn-primary">بدء التثبيت الآن</a>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>خطأ: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في تحليل الاستجابة</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
