<?php
/**
 * تسجيل دخول مبسط للإدارة - ExRayan Platform
 */

// تحميل الملفات الأساسية
require_once __DIR__ . '/../includes/Database.php';
require_once __DIR__ . '/../includes/Security.php';
require_once __DIR__ . '/../includes/Session.php';

// بدء الجلسة
Session::start();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username)) {
            throw new Exception('اسم المستخدم مطلوب');
        }
        
        if (empty($password)) {
            throw new Exception('كلمة المرور مطلوبة');
        }
        
        // البحث عن المستخدم
        $db = Database::getInstance();
        $user = $db->selectOne(
            "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );
        
        if (!$user) {
            throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
        
        // التحقق من كلمة المرور
        if (!password_verify($password, $user['password'])) {
            throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
        
        // التحقق من صلاحيات الإدارة
        if (!in_array($user['classification'], ['admin', 'moderator'])) {
            throw new Exception('ليس لديك صلاحية للوصول لهذه الصفحة. تصنيفك: ' . $user['classification']);
        }
        
        // تسجيل الدخول
        Session::login($user);
        
        // تحديث آخر تسجيل دخول
        $db->update(
            "UPDATE users SET last_login_at = NOW() WHERE id = ?",
            [$user['id']]
        );
        
        // إعادة التوجيه للوحة التحكم
        header('Location: index.php');
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم | ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 20px; }
        .login-container { background: white; border-radius: 20px; padding: 40px; max-width: 400px; width: 100%; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .login-title { font-size: 2rem; font-weight: 700; margin-bottom: 10px; color: #333; text-align: center; }
        .login-subtitle { color: #666; margin-bottom: 30px; text-align: center; }
        .form-control { border: 2px solid #e9ecef; border-radius: 10px; padding: 15px; font-size: 1rem; margin-bottom: 20px; }
        .form-control:focus { border-color: #667eea; box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25); }
        .btn-login { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white; padding: 15px; border-radius: 10px; font-weight: 600; font-size: 1.1rem; width: 100%; }
        .btn-login:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3); }
        .alert { border-radius: 10px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="text-center mb-4">
            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
            <h1 class="login-title">لوحة التحكم</h1>
            <p class="login-subtitle">مرحباً بك في نظام إدارة ExRayan</p>
        </div>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <?= htmlspecialchars($success) ?>
        </div>
        <?php endif; ?>

        <form method="POST">
            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-user me-1"></i>
                    اسم المستخدم أو البريد الإلكتروني
                </label>
                <input type="text" name="username" class="form-control"
                       value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                       placeholder="أدخل اسم المستخدم" required>
            </div>

            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-lock me-1"></i>
                    كلمة المرور
                </label>
                <input type="password" name="password" class="form-control"
                       placeholder="أدخل كلمة المرور" required>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>

        <div class="text-center mt-4">
            <a href="../" class="text-muted">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للموقع الرئيسي
            </a>
        </div>
        
        <div class="mt-4 p-3 bg-light rounded">
            <h6>بيانات الدخول الافتراضية:</h6>
            <p class="mb-1"><strong>اسم المستخدم:</strong> admin</p>
            <p class="mb-0"><strong>كلمة المرور:</strong> admin123</p>
        </div>
    </div>
</body>
</html>
