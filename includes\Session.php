<?php
/**
 * فئة إدارة الجلسات - ExRayan Platform
 * Session Management Class
 */

class Session {
    
    /**
     * بدء الجلسة
     */
    public static function start() {
        if (session_status() === PHP_SESSION_NONE) {
            // إعدادات الجلسة الآمنة
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            session_start();
            
            // تجديد معرف الجلسة دورياً
            if (!isset($_SESSION['last_regeneration'])) {
                self::regenerateId();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
                self::regenerateId();
            }
        }
    }
    
    /**
     * تجديد معرف الجلسة
     */
    public static function regenerateId() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * تعيين قيمة في الجلسة
     */
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * الحصول على قيمة من الجلسة
     */
    public static function get($key, $default = null) {
        self::start();
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * التحقق من وجود مفتاح في الجلسة
     */
    public static function has($key) {
        self::start();
        return isset($_SESSION[$key]);
    }
    
    /**
     * حذف مفتاح من الجلسة
     */
    public static function remove($key) {
        self::start();
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * مسح جميع بيانات الجلسة
     */
    public static function clear() {
        self::start();
        $_SESSION = [];
    }
    
    /**
     * تدمير الجلسة
     */
    public static function destroy() {
        self::start();
        
        // مسح بيانات الجلسة
        $_SESSION = [];
        
        // حذف كوكي الجلسة
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // تدمير الجلسة
        session_destroy();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public static function login($user) {
        self::start();
        self::regenerateId();
        
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['role_id'] = $user['role_id'];
        $_SESSION['classification'] = $user['classification'];
        $_SESSION['is_logged_in'] = true;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // تسجيل عملية تسجيل الدخول
        self::logLoginActivity($user['id'], 'success');
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public static function logout() {
        $userId = self::get('user_id');
        
        if ($userId) {
            self::logLoginActivity($userId, 'logout');
        }
        
        self::destroy();
    }
    
    /**
     * التحقق من تسجيل دخول المستخدم
     */
    public static function isLoggedIn() {
        self::start();
        
        if (!isset($_SESSION['is_logged_in']) || !$_SESSION['is_logged_in']) {
            return false;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        $sessionTimeout = 3600; // ساعة واحدة
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $sessionTimeout)) {
            self::logout();
            return false;
        }
        
        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * الحصول على معرف المستخدم الحالي
     */
    public static function getUserId() {
        return self::isLoggedIn() ? self::get('user_id') : null;
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public static function getUser() {
        if (!self::isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => self::get('user_id'),
            'username' => self::get('username'),
            'email' => self::get('email'),
            'role_id' => self::get('role_id'),
            'classification' => self::get('classification')
        ];
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($permission) {
        if (!self::isLoggedIn()) {
            return false;
        }
        
        $roleId = self::get('role_id');
        if (!$roleId) {
            return false;
        }
        
        // جلب صلاحيات الدور من قاعدة البيانات
        $db = Database::getInstance();
        $role = $db->selectOne("SELECT permissions FROM roles WHERE id = :id AND is_active = 1", ['id' => $roleId]);
        
        if (!$role || !$role['permissions']) {
            return false;
        }
        
        $permissions = json_decode($role['permissions'], true);
        return in_array($permission, $permissions);
    }
    
    /**
     * إضافة رسالة فلاش
     */
    public static function flash($key, $message) {
        self::start();
        $_SESSION['flash'][$key] = $message;
    }
    
    /**
     * الحصول على رسالة فلاش
     */
    public static function getFlash($key) {
        self::start();
        
        if (isset($_SESSION['flash'][$key])) {
            $message = $_SESSION['flash'][$key];
            unset($_SESSION['flash'][$key]);
            return $message;
        }
        
        return null;
    }
    
    /**
     * التحقق من وجود رسائل فلاش
     */
    public static function hasFlash($key = null) {
        self::start();
        
        if ($key) {
            return isset($_SESSION['flash'][$key]);
        }
        
        return !empty($_SESSION['flash']);
    }
    
    /**
     * تسجيل نشاط تسجيل الدخول
     */
    private static function logLoginActivity($userId, $status) {
        $db = Database::getInstance();
        
        $logData = [
            'user_id' => $userId,
            'ip_address' => Security::getRealIpAddress(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'status' => $status,
            'session_id' => session_id()
        ];
        
        $query = "INSERT INTO login_logs (user_id, ip_address, user_agent, status, session_id) 
                  VALUES (:user_id, :ip_address, :user_agent, :status, :session_id)";
        
        $db->insert($query, $logData);
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public static function cleanupExpiredSessions() {
        $db = Database::getInstance();
        
        // حذف سجلات تسجيل الدخول القديمة (أكثر من 30 يوم)
        $query = "DELETE FROM login_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $db->delete($query);
        
        // حذف سجلات النشاطات القديمة (أكثر من 90 يوم)
        $query = "DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)";
        $db->delete($query);
    }
}
