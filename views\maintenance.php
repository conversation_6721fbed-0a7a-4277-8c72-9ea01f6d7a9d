<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموقع تحت الصيانة - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .maintenance-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 40px;
        }
        
        .maintenance-icon {
            font-size: 6rem;
            margin-bottom: 2rem;
            animation: spin 3s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .maintenance-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .countdown {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 2rem 0;
        }
        
        .countdown-item {
            display: inline-block;
            margin: 0 10px;
            text-align: center;
        }
        
        .countdown-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .countdown-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .social-links {
            margin-top: 2rem;
        }
        
        .social-link {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: transform 0.3s;
        }
        
        .social-link:hover {
            color: white;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="fas fa-cog"></i>
        </div>
        
        <h1 class="maintenance-title">الموقع تحت الصيانة</h1>
        
        <p class="maintenance-message">
            نحن نعمل على تحسين الموقع لتقديم تجربة أفضل لك. سنعود قريباً!
        </p>
        
        <div class="countdown">
            <div class="countdown-item">
                <span class="countdown-number" id="hours">00</span>
                <div class="countdown-label">ساعة</div>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">00</span>
                <div class="countdown-label">دقيقة</div>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="seconds">00</span>
                <div class="countdown-label">ثانية</div>
            </div>
        </div>
        
        <p>يمكنك متابعتنا على وسائل التواصل الاجتماعي للحصول على آخر التحديثات:</p>
        
        <div class="social-links">
            <a href="#" class="social-link">
                <i class="fab fa-facebook"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i>
            </a>
        </div>
        
        <div class="mt-4">
            <p class="mb-0">للاستفسارات العاجلة:</p>
            <a href="mailto:<EMAIL>" class="text-white">
                <i class="fas fa-envelope me-1"></i>
                <EMAIL>
            </a>
        </div>
    </div>
    
    <script>
        // العد التنازلي (مثال: 2 ساعة من الآن)
        const targetTime = new Date().getTime() + (2 * 60 * 60 * 1000);
        
        function updateCountdown() {
            const now = new Date().getTime();
            const distance = targetTime - now;
            
            if (distance > 0) {
                const hours = Math.floor(distance / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            } else {
                // انتهى الوقت - إعادة تحميل الصفحة
                location.reload();
            }
        }
        
        // تحديث العد التنازلي كل ثانية
        setInterval(updateCountdown, 1000);
        updateCountdown();
    </script>
</body>
</html>
