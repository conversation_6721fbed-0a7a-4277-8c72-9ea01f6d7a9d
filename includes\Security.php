<?php
/**
 * فئة الأمان - ExRayan Platform
 * Security Class
 */

class Security {
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * توليد رمز عشوائي آمن
     */
    public static function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * تنظيف البيانات المدخلة
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        return $input;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * التحقق من رقم الهاتف
     */
    public static function validatePhone($phone) {
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // التحقق من الطول والتنسيق
        if (preg_match('/^\+?[1-9]\d{7,14}$/', $phone)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    public static function getRealIpAddress() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * التحقق من CSRF Token
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * توليد CSRF Token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = self::generateSecureToken();
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    public static function encrypt($data, $key = null) {
        $key = $key ?: $_ENV['APP_KEY'] ?? 'default-encryption-key';
        $cipher = 'AES-256-GCM';
        $ivlen = openssl_cipher_iv_length($cipher);
        $iv = openssl_random_pseudo_bytes($ivlen);
        $tag = '';
        
        $encrypted = openssl_encrypt($data, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
        
        return base64_encode($iv . $tag . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($encryptedData, $key = null) {
        $key = $key ?: $_ENV['APP_KEY'] ?? 'default-encryption-key';
        $cipher = 'AES-256-GCM';
        $ivlen = openssl_cipher_iv_length($cipher);
        $taglen = 16;
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, $ivlen);
        $tag = substr($data, $ivlen, $taglen);
        $encrypted = substr($data, $ivlen + $taglen);
        
        return openssl_decrypt($encrypted, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
    }
    
    /**
     * تسجيل محاولة أمنية مشبوهة
     */
    public static function logSuspiciousActivity($userId, $action, $details = []) {
        $db = Database::getInstance();
        
        $logData = [
            'user_id' => $userId,
            'action' => $action,
            'module' => 'security',
            'ip_address' => self::getRealIpAddress(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'result' => 'blocked',
            'details' => json_encode($details)
        ];
        
        $query = "INSERT INTO activity_logs (user_id, action, module, ip_address, user_agent, result, details) 
                  VALUES (:user_id, :action, :module, :ip_address, :user_agent, :result, :details)";
        
        return $db->insert($query, $logData);
    }
    
    /**
     * التحقق من معدل الطلبات (Rate Limiting)
     */
    public static function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
        $key = "rate_limit:" . $identifier;
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
        }
        
        $data = $_SESSION[$key];
        
        // إعادة تعيين العداد إذا انتهت النافذة الزمنية
        if (time() - $data['first_attempt'] > $timeWindow) {
            $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
            return true;
        }
        
        // زيادة العداد
        $_SESSION[$key]['count']++;
        
        return $_SESSION[$key]['count'] <= $maxAttempts;
    }
}
