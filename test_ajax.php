<?php
/**
 * اختبار AJAX - ExRayan Platform
 * AJAX Test Script
 */

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'test') {
        echo json_encode([
            'success' => true,
            'message' => 'AJAX يعمل بشكل صحيح!',
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $_POST
        ]);
        exit;
    }
    
    if ($action === 'test_db') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists(__DIR__ . '/.env')) {
                $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_POST['db_host'] ?? $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_POST['db_name'] ?? $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_POST['db_user'] ?? $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_POST['db_pass'] ?? $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // اختبار استعلام بسيط
            $result = $pdo->query("SELECT VERSION() as version, NOW() as current_time");
            $info = $result->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم الاتصال بقاعدة البيانات بنجاح',
                'mysql_version' => $info['version'],
                'current_time' => $info['current_time'],
                'connection_info' => [
                    'host' => $host,
                    'database' => $dbname,
                    'username' => $username
                ]
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
        }
        exit;
    }
    
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار AJAX - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .test-container { background: white; border-radius: 15px; padding: 30px; max-width: 800px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-test { background: #667eea; border: none; color: white; padding: 10px 20px; border-radius: 8px; margin: 5px; }
        .btn-test:hover { background: #5a6fd8; }
        .result-box { background: #f8f9fa; border-radius: 8px; padding: 15px; margin-top: 15px; min-height: 100px; }
        .alert { border-radius: 8px; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-flask text-primary"></i>
            اختبار AJAX
        </h1>
        
        <div class="row">
            <div class="col-md-6">
                <h5>اختبار AJAX البسيط</h5>
                <button class="btn btn-test" onclick="testAjax()">
                    <i class="fas fa-play"></i>
                    اختبار AJAX
                </button>
                <div id="ajaxResult" class="result-box"></div>
            </div>
            
            <div class="col-md-6">
                <h5>اختبار قاعدة البيانات</h5>
                <form id="dbTestForm">
                    <div class="mb-2">
                        <input type="text" name="db_host" class="form-control form-control-sm" placeholder="الخادم" value="127.0.0.1">
                    </div>
                    <div class="mb-2">
                        <input type="text" name="db_name" class="form-control form-control-sm" placeholder="قاعدة البيانات" value="exrayan">
                    </div>
                    <div class="mb-2">
                        <input type="text" name="db_user" class="form-control form-control-sm" placeholder="المستخدم" value="root">
                    </div>
                    <div class="mb-2">
                        <input type="password" name="db_pass" class="form-control form-control-sm" placeholder="كلمة المرور" value="000">
                    </div>
                    <button type="button" class="btn btn-test" onclick="testDatabase()">
                        <i class="fas fa-database"></i>
                        اختبار قاعدة البيانات
                    </button>
                </form>
                <div id="dbResult" class="result-box"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <h5>معلومات النظام</h5>
            <div class="alert alert-info">
                <strong>PHP Version:</strong> <?= PHP_VERSION ?><br>
                <strong>Server:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?><br>
                <strong>Document Root:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?><br>
                <strong>Script Name:</strong> <?= $_SERVER['SCRIPT_NAME'] ?? 'Unknown' ?>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="install.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>
                العودة للتثبيت
            </a>
            <a href="welcome.php" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                الصفحة الرئيسية
            </a>
        </div>
    </div>
    
    <script>
        function testAjax() {
            const resultDiv = document.getElementById('ajaxResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
            
            const formData = new FormData();
            formData.append('action', 'test');
            formData.append('test_data', 'Hello from AJAX!');
            
            fetch('test_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>نجح الاختبار!</strong><br>
                                الرسالة: ${data.message}<br>
                                الوقت: ${data.timestamp}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger">فشل: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>خطأ في تحليل JSON:</strong><br>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
            });
        }
        
        function testDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار قاعدة البيانات...';
            
            const formData = new FormData(document.getElementById('dbTestForm'));
            formData.append('action', 'test_db');
            
            fetch('test_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                console.log('DB Response:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>نجح الاتصال!</strong><br>
                                الرسالة: ${data.message}<br>
                                إصدار MySQL: ${data.mysql_version}<br>
                                الوقت الحالي: ${data.current_time}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger">فشل: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>خطأ في تحليل JSON:</strong><br>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
