<?php
/**
 * فئة التحقق من صحة البيانات - ExRayan Platform
 * Validation Class
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    /**
     * التحقق من وجود الحقل
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field][] = $message ?: "حقل {$field} مطلوب";
        }
        return $this;
    }
    
    /**
     * التحقق من البريد الإلكتروني
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!Security::validateEmail($this->data[$field])) {
                $this->errors[$field][] = $message ?: "البريد الإلكتروني غير صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأدنى للطول
     */
    public function min($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field][] = $message ?: "حقل {$field} يجب أن يكون {$length} أحرف على الأقل";
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأقصى للطول
     */
    public function max($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field][] = $message ?: "حقل {$field} يجب أن يكون {$length} أحرف كحد أقصى";
        }
        return $this;
    }
    
    /**
     * التحقق من تطابق الحقول
     */
    public function match($field, $matchField, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$matchField])) {
            if ($this->data[$field] !== $this->data[$matchField]) {
                $this->errors[$field][] = $message ?: "حقل {$field} لا يتطابق مع {$matchField}";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة رقمية
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field][] = $message ?: "حقل {$field} يجب أن يكون رقماً";
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة عدد صحيح
     */
    public function integer($field, $message = null) {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
            $this->errors[$field][] = $message ?: "حقل {$field} يجب أن يكون عدداً صحيحاً";
        }
        return $this;
    }
    
    /**
     * التحقق من رقم الهاتف
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!Security::validatePhone($this->data[$field])) {
                $this->errors[$field][] = $message ?: "رقم الهاتف غير صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة ضمن قائمة محددة
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field][] = $message ?: "قيمة حقل {$field} غير صحيحة";
        }
        return $this;
    }
    
    /**
     * التحقق من التاريخ
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field][] = $message ?: "تاريخ حقل {$field} غير صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من الرابط
     */
    public function url($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_URL)) {
                $this->errors[$field][] = $message ?: "رابط حقل {$field} غير صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من وجود القيمة في قاعدة البيانات
     */
    public function exists($field, $table, $column = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?: $field;
            $db = Database::getInstance();
            
            $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = :value";
            $result = $db->selectOne($query, ['value' => $this->data[$field]]);
            
            if (!$result || $result['count'] == 0) {
                $this->errors[$field][] = $message ?: "قيمة حقل {$field} غير موجودة";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من عدم وجود القيمة في قاعدة البيانات (للتأكد من الفرادة)
     */
    public function unique($field, $table, $column = null, $except = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?: $field;
            $db = Database::getInstance();
            
            $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = :value";
            $params = ['value' => $this->data[$field]];
            
            if ($except) {
                $query .= " AND id != :except";
                $params['except'] = $except;
            }
            
            $result = $db->selectOne($query, $params);
            
            if ($result && $result['count'] > 0) {
                $this->errors[$field][] = $message ?: "قيمة حقل {$field} موجودة مسبقاً";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من نوع الملف
     */
    public function fileType($field, $allowedTypes, $message = null) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
            $fileType = $_FILES[$field]['type'];
            $extension = strtolower(pathinfo($_FILES[$field]['name'], PATHINFO_EXTENSION));
            
            if (!in_array($fileType, $allowedTypes) && !in_array($extension, $allowedTypes)) {
                $this->errors[$field][] = $message ?: "نوع الملف غير مسموح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من حجم الملف
     */
    public function fileSize($field, $maxSize, $message = null) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
            if ($_FILES[$field]['size'] > $maxSize) {
                $maxSizeMB = round($maxSize / 1024 / 1024, 2);
                $this->errors[$field][] = $message ?: "حجم الملف يجب أن يكون أقل من {$maxSizeMB} ميجابايت";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة كلمة المرور
     */
    public function password($field, $message = null) {
        if (isset($this->data[$field])) {
            $result = Security::validatePassword($this->data[$field]);
            if ($result !== true) {
                $this->errors[$field] = array_merge($this->errors[$field] ?? [], $result);
            }
        }
        return $this;
    }
    
    /**
     * التحقق من وجود أخطاء
     */
    public function fails() {
        return !empty($this->errors);
    }
    
    /**
     * الحصول على الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * الحصول على أول خطأ لحقل معين
     */
    public function getFirstError($field) {
        return isset($this->errors[$field]) ? $this->errors[$field][0] : null;
    }
    
    /**
     * إضافة خطأ مخصص
     */
    public function addError($field, $message) {
        $this->errors[$field][] = $message;
        return $this;
    }
}
