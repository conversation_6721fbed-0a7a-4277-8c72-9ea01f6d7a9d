<?php
/**
 * إصلاح مخطط قاعدة البيانات - ExRayan Platform
 * Database Schema Fix Tool
 */

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'fix_schema') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $fixes = [];
            
            // إصلاح جدول auctions
            try {
                $pdo->exec("ALTER TABLE auctions MODIFY COLUMN end_date timestamp NULL DEFAULT NULL");
                $fixes[] = "تم إصلاح حقل end_date في جدول auctions";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), "doesn't exist") === false) {
                    $fixes[] = "خطأ في إصلاح جدول auctions: " . $e->getMessage();
                }
            }
            
            // إصلاح جدول ad_campaigns
            try {
                $pdo->exec("ALTER TABLE ad_campaigns MODIFY COLUMN end_date timestamp NULL DEFAULT NULL");
                $fixes[] = "تم إصلاح حقل end_date في جدول ad_campaigns";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), "doesn't exist") === false) {
                    $fixes[] = "خطأ في إصلاح جدول ad_campaigns: " . $e->getMessage();
                }
            }
            
            // إصلاح مشاكل أخرى محتملة
            $commonFixes = [
                "ALTER TABLE users MODIFY COLUMN email_verified_at timestamp NULL DEFAULT NULL",
                "ALTER TABLE users MODIFY COLUMN last_login_at timestamp NULL DEFAULT NULL",
                "ALTER TABLE ads MODIFY COLUMN expires_at timestamp NULL DEFAULT NULL",
                "ALTER TABLE stores MODIFY COLUMN verified_at timestamp NULL DEFAULT NULL"
            ];
            
            foreach ($commonFixes as $fix) {
                try {
                    $pdo->exec($fix);
                    $tableName = preg_match('/ALTER TABLE (\w+)/', $fix, $matches) ? $matches[1] : 'unknown';
                    $fixes[] = "تم إصلاح جدول $tableName";
                } catch (Exception $e) {
                    // تجاهل الأخطاء للجداول غير الموجودة
                    if (strpos($e->getMessage(), "doesn't exist") === false && 
                        strpos($e->getMessage(), "Unknown column") === false) {
                        $fixes[] = "تحذير: " . $e->getMessage();
                    }
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إصلاح مخطط قاعدة البيانات بنجاح',
                'fixes' => $fixes
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    if ($action === 'check_schema') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $issues = [];
            
            // فحص الجداول المشكوك فيها
            $tablesToCheck = ['auctions', 'ad_campaigns', 'users', 'ads', 'stores'];
            
            foreach ($tablesToCheck as $table) {
                try {
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    foreach ($columns as $column) {
                        if (($column['Type'] === 'timestamp' || $column['Type'] === 'datetime') && 
                            $column['Null'] === 'NO' && 
                            $column['Default'] === null &&
                            !in_array($column['Field'], ['created_at', 'updated_at'])) {
                            
                            $issues[] = [
                                'table' => $table,
                                'column' => $column['Field'],
                                'type' => $column['Type'],
                                'issue' => 'حقل تاريخ مطلوب بدون قيمة افتراضية'
                            ];
                        }
                    }
                } catch (Exception $e) {
                    // الجدول غير موجود
                }
            }
            
            echo json_encode([
                'success' => true,
                'issues' => $issues,
                'message' => count($issues) > 0 ? 'تم العثور على ' . count($issues) . ' مشكلة' : 'لا توجد مشاكل في المخطط'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مخطط قاعدة البيانات - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); min-height: 100vh; padding: 20px; }
        .fix-container { background: white; border-radius: 15px; padding: 30px; max-width: 800px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-fix { background: #ffc107; border: none; color: #212529; padding: 10px 20px; border-radius: 8px; font-weight: 600; margin: 5px; }
        .btn-fix:hover { background: #e0a800; }
        .issue-item { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 10px; }
        .alert { border-radius: 8px; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-database text-warning"></i> إصلاح مخطط قاعدة البيانات</h1>
            <p class="text-muted">أداة لإصلاح مشاكل حقول التاريخ في قاعدة البيانات</p>
        </div>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>حول هذه الأداة:</h5>
            <p class="mb-0">
                تقوم هذه الأداة بإصلاح مشاكل حقول التاريخ (timestamp) التي تسبب خطأ "Invalid default value".
                المشكلة تحدث عندما يكون حقل التاريخ مطلوب (NOT NULL) بدون قيمة افتراضية.
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-search me-2"></i>
                            فحص المشاكل
                        </h5>
                        <p class="card-text">
                            فحص قاعدة البيانات للبحث عن مشاكل في حقول التاريخ
                        </p>
                        <button class="btn btn-fix" onclick="checkSchema()">
                            <i class="fas fa-search me-2"></i>
                            فحص المخطط
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح المشاكل
                        </h5>
                        <p class="card-text">
                            إصلاح جميع مشاكل حقول التاريخ تلقائياً
                        </p>
                        <button class="btn btn-fix" onclick="fixSchema()">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح المخطط
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="result" class="mt-4"></div>
        
        <div class="text-center mt-4">
            <a href="welcome.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <a href="install.php" class="btn btn-primary">
                <i class="fas fa-download me-2"></i>
                إعادة التثبيت
            </a>
        </div>
        
        <div class="mt-4">
            <h6>المشاكل الشائعة التي يتم إصلاحها:</h6>
            <ul class="small text-muted">
                <li>حقل end_date في جدول auctions</li>
                <li>حقل end_date في جدول ad_campaigns</li>
                <li>حقول التاريخ الاختيارية في جداول أخرى</li>
                <li>مشاكل القيم الافتراضية للحقول المطلوبة</li>
            </ul>
        </div>
    </div>
    
    <script>
        function checkSchema() {
            executeAction('check_schema', {}, 'فحص مخطط قاعدة البيانات');
        }
        
        function fixSchema() {
            if (!confirm('هل تريد إصلاح مخطط قاعدة البيانات؟\n\nسيتم تعديل بعض حقول الجداول.')) {
                return;
            }
            
            executeAction('fix_schema', {}, 'إصلاح مخطط قاعدة البيانات');
        }
        
        function executeAction(action, data, actionName) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري ${actionName}...</div>`;
            
            const formData = new FormData();
            formData.append('action', action);
            
            for (const [key, value] of Object.entries(data)) {
                formData.append(key, value);
            }
            
            fetch('fix_database_schema.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const result = JSON.parse(text);
                    if (result.success) {
                        let html = `<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>${result.message}`;
                        
                        if (result.fixes && result.fixes.length > 0) {
                            html += '<br><br><strong>الإصلاحات المنجزة:</strong><ul>';
                            result.fixes.forEach(fix => {
                                html += `<li>${fix}</li>`;
                            });
                            html += '</ul>';
                        }
                        
                        if (result.issues && result.issues.length > 0) {
                            html += '<br><br><strong>المشاكل المكتشفة:</strong>';
                            result.issues.forEach(issue => {
                                html += `<div class="issue-item">
                                    <strong>جدول ${issue.table}:</strong> حقل ${issue.column} (${issue.type})<br>
                                    <small>${issue.issue}</small>
                                </div>`;
                            });
                        }
                        
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>خطأ: ${result.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في تحليل الاستجابة</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
