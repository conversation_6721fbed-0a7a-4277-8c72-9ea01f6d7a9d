# 🚀 دليل التثبيت السريع - ExRayan Platform

## 📋 المتطلبات الأساسية

### متطلبات الخادم
- **PHP 7.4+** مع الإضافات التالية:
  - mysqli أو pdo_mysql
  - json
  - openssl
  - mbstring
  - fileinfo
- **MySQL 5.7+** أو **MariaDB 10.2+**
- **Apache** مع mod_rewrite أو **Nginx**
- **SSL Certificate** (للإنتاج)

### متطلبات اختيارية
- **Redis** (للتخزين المؤقت)
- **Composer** (لإدارة المكتبات)
- **Node.js** (لأدوات التطوير)

---

## ⚡ التثبيت السريع

### 1. تحميل المشروع
```bash
# استنساخ المشروع
git clone https://github.com/exrayan/classified-ads.git exrayan
cd exrayan

# أو تحميل الملفات مباشرة إلى مجلد الويب
# مثال: d:\xampp\htdocs\exrayan
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'exrayan_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON exrayan.* TO 'exrayan_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. استيراد هيكل قاعدة البيانات
```bash
# استيراد الهيكل
mysql -u root -p exrayan < database_schema.sql

# أو من phpMyAdmin
# 1. اختر قاعدة البيانات exrayan
# 2. اذهب لتبويب "استيراد"
# 3. اختر ملف database_schema.sql
# 4. اضغط "تنفيذ"
```

### 4. إعداد ملف البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# تحرير الإعدادات
nano .env
```

### 5. إعداد الصلاحيات
```bash
# إعداد صلاحيات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 .env

# في Windows (من Command Prompt كمدير)
icacls uploads /grant Everyone:F
icacls logs /grant Everyone:F
icacls backups /grant Everyone:F
```

---

## 🔧 التثبيت التفاعلي

### استخدام ملف التثبيت
1. افتح المتصفح واذهب إلى: `http://localhost/exrayan/install.php`
2. اتبع الخطوات التفاعلية:
   - فحص المتطلبات
   - إعداد قاعدة البيانات
   - إنشاء حساب المدير
   - اكتمال التثبيت

---

## ⚙️ الإعدادات المهمة

### إعدادات قاعدة البيانات (.env)
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=exrayan
DB_USERNAME=root
DB_PASSWORD=000
```

### إعدادات الأمان
```env
APP_KEY=your-32-character-secret-key
SECURITY_2FA_ENABLED=true
SECURITY_MAX_LOGIN_ATTEMPTS=5
```

### إعدادات البريد (اختياري)
```env
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

---

## 🏃‍♂️ التشغيل السريع

### 1. إنشاء حساب مدير يدوياً
```php
<?php
// أضف هذا الكود في ملف مؤقت وشغله مرة واحدة
require_once 'includes/Database.php';
require_once 'includes/Security.php';

$db = Database::getInstance();
$password = Security::hashPassword('admin123');

$db->insert("
    INSERT INTO users (username, email, password, role_id, classification, is_active, email_verified_at) 
    VALUES (?, ?, ?, 1, 'admin', 1, NOW())
", ['admin', '<EMAIL>', $password]);

echo "تم إنشاء حساب المدير بنجاح!";
?>
```

### 2. الوصول للموقع
- **الصفحة الرئيسية:** `http://localhost/exrayan/`
- **لوحة التحكم:** `http://localhost/exrayan/admin/`
- **بيانات الدخول:** admin / admin123

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ الاتصال بقاعدة البيانات
```
الحل:
1. تأكد من تشغيل MySQL
2. تحقق من بيانات الاتصال في .env
3. تأكد من وجود قاعدة البيانات
```

#### خطأ الصلاحيات
```
الحل:
1. تأكد من صلاحيات مجلدات uploads و logs
2. في Linux: sudo chown -R www-data:www-data /path/to/exrayan
3. في Windows: إعطاء صلاحيات كاملة للمجلدات
```

#### خطأ 404 في الروابط
```
الحل:
1. تأكد من تفعيل mod_rewrite في Apache
2. تحقق من وجود ملف .htaccess
3. في Nginx: إعداد try_files بشكل صحيح
```

#### خطأ في رفع الملفات
```
الحل:
1. تحقق من إعدادات PHP:
   - upload_max_filesize = 10M
   - post_max_size = 10M
   - max_execution_time = 300
2. تأكد من صلاحيات مجلد uploads
```

---

## 🌐 إعداد الإنتاج

### 1. إعدادات الأمان
```env
APP_ENV=production
APP_DEBUG=false
SECURITY_2FA_ENABLED=true
```

### 2. إعداد HTTPS
```apache
# في .htaccess
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 3. إعداد النسخ الاحتياطي
```bash
# إضافة مهمة cron للنسخ الاحتياطي اليومي
0 2 * * * /usr/bin/php /path/to/exrayan/backup.php
```

---

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل:
1. **تحقق من ملفات السجلات:** `logs/app_errors.log`
2. **راجع الوثائق:** [docs.exrayan.com](https://docs.exrayan.com)
3. **تواصل معنا:** <EMAIL>
4. **GitHub Issues:** [github.com/exrayan/issues](https://github.com/exrayan/issues)

---

## ✅ قائمة التحقق النهائية

- [ ] تم إنشاء قاعدة البيانات
- [ ] تم استيراد ملف database_schema.sql
- [ ] تم إعداد ملف .env
- [ ] تم إعداد صلاحيات المجلدات
- [ ] تم إنشاء حساب المدير
- [ ] يعمل الموقع بدون أخطاء
- [ ] تم اختبار رفع الملفات
- [ ] تم اختبار إرسال البريد (اختياري)

---

**🎉 مبروك! تم تثبيت ExRayan بنجاح!**

يمكنك الآن البدء في استخدام منصة الإعلانات المبوبة الأكثر تطوراً في المنطقة العربية.
