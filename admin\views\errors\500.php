<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم - لوحة التحكم | ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .error-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 2rem;
        }
        
        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-block;
            margin: 0 10px;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
        }
        
        .error-details h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .error-details ul {
            color: #6c757d;
            margin: 0;
        }
        
        .error-details li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <div class="error-code">500</div>
        <h1 class="error-title">خطأ في الخادم</h1>
        
        <p class="error-message">
            عذراً، حدث خطأ غير متوقع في الخادم أثناء معالجة طلبك.<br>
            نحن نعمل على حل هذه المشكلة في أسرع وقت ممكن.
        </p>
        
        <div class="mt-4">
            <a href="index.php" class="btn-home">
                <i class="fas fa-tachometer-alt me-2"></i>
                العودة للوحة التحكم
            </a>
            
            <a href="../" class="btn-home btn-secondary">
                <i class="fas fa-home me-2"></i>
                الموقع الرئيسي
            </a>
        </div>
        
        <div class="error-details">
            <h6>خطوات حل المشكلة:</h6>
            <ul class="text-start">
                <li>تحديث الصفحة والمحاولة مرة أخرى</li>
                <li>التحقق من اتصال الإنترنت</li>
                <li>مراجعة ملفات السجلات للحصول على تفاصيل أكثر</li>
                <li>التواصل مع المطور إذا استمرت المشكلة</li>
            </ul>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                وقت الخطأ: <?= date('Y-m-d H:i:s') ?>
            </small>
        </div>
    </div>
    
    <script>
        // إعادة المحاولة التلقائية بعد 30 ثانية
        setTimeout(() => {
            if (confirm('هل تريد إعادة المحاولة؟')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
