<?php
/**
 * إعداد سريع للمشروع - ExRayan Platform
 * Quick Project Setup
 */

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');
    
    try {
        // تحميل متغيرات البيئة
        if (file_exists('.env')) {
            $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value, '"\'');
                }
            }
        }
        
        $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
        $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '000';
        
        // الاتصال بالخادم
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // حذف قاعدة البيانات إذا كانت موجودة
        $pdo->exec("DROP DATABASE IF EXISTS `$dbname`");
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // الاتصال بقاعدة البيانات الجديدة
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء الجداول الأساسية فقط
        $basicTables = "
        -- جدول المستخدمين
        CREATE TABLE users (
            id int(11) NOT NULL AUTO_INCREMENT,
            username varchar(50) NOT NULL,
            email varchar(100) NOT NULL,
            password varchar(255) NOT NULL,
            role_id int(11) DEFAULT 1,
            classification enum('admin','moderator','user','advertiser','store_owner') DEFAULT 'user',
            is_active tinyint(1) DEFAULT 1,
            email_verified_at timestamp NULL DEFAULT NULL,
            created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_username (username),
            UNIQUE KEY unique_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        
        -- جدول الأقسام
        CREATE TABLE sections (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            description text,
            icon varchar(50) DEFAULT NULL,
            is_active tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        
        -- جدول الإعلانات
        CREATE TABLE ads (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL,
            section_id int(11) NOT NULL,
            title varchar(255) NOT NULL,
            description text NOT NULL,
            price decimal(15,2) DEFAULT NULL,
            status enum('active','inactive','pending','sold','expired') DEFAULT 'pending',
            is_featured tinyint(1) DEFAULT 0,
            views_count int(11) DEFAULT 0,
            created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_user (user_id),
            KEY idx_section (section_id),
            KEY idx_status (status),
            CONSTRAINT fk_ads_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            CONSTRAINT fk_ads_section FOREIGN KEY (section_id) REFERENCES sections (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        
        -- جدول الإعدادات
        CREATE TABLE settings (
            id int(11) NOT NULL AUTO_INCREMENT,
            setting_key varchar(100) NOT NULL,
            setting_value text,
            description text,
            created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        // تنفيذ الجداول
        $statements = explode(';', $basicTables);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // إنشاء حساب المدير
        $adminUsername = 'admin';
        $adminEmail = '<EMAIL>';
        $adminPassword = password_hash('admin123', PASSWORD_ARGON2ID);
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role_id, classification, is_active, email_verified_at) VALUES (?, ?, ?, 1, 'admin', 1, NOW())");
        $stmt->execute([$adminUsername, $adminEmail, $adminPassword]);
        
        // إضافة بعض الأقسام الأساسية
        $sections = [
            ['سيارات', 'سيارات ومركبات', 'fas fa-car'],
            ['عقارات', 'بيع وإيجار العقارات', 'fas fa-home'],
            ['إلكترونيات', 'أجهزة إلكترونية', 'fas fa-laptop'],
            ['أثاث', 'أثاث منزلي', 'fas fa-couch'],
            ['ملابس', 'ملابس وإكسسوارات', 'fas fa-tshirt']
        ];
        
        foreach ($sections as $index => $section) {
            $stmt = $pdo->prepare("INSERT INTO sections (name, description, icon, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([$section[0], $section[1], $section[2], $index + 1]);
        }
        
        // إضافة إعدادات أساسية
        $settings = [
            ['site_name', 'ExRayan', 'اسم الموقع'],
            ['site_description', 'منصة الإعلانات المبوبة', 'وصف الموقع'],
            ['admin_email', '<EMAIL>', 'بريد المدير'],
            ['currency', 'KWD', 'العملة الافتراضية'],
            ['timezone', 'Asia/Kuwait', 'المنطقة الزمنية']
        ];
        
        foreach ($settings as $setting) {
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
        
        // إنشاء ملف التثبيت
        file_put_contents('.installed', date('Y-m-d H:i:s'));
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إعداد المشروع بنجاح!',
            'admin_credentials' => [
                'username' => $adminUsername,
                'email' => $adminEmail,
                'password' => 'admin123'
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد سريع - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .setup-container { background: white; border-radius: 15px; padding: 40px; max-width: 600px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-setup { background: #667eea; border: none; color: white; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 1.1rem; }
        .btn-setup:hover { background: #5a6fd8; }
        .alert { border-radius: 8px; margin-top: 20px; }
        .credentials-box { background: #f8f9fa; border: 2px solid #28a745; border-radius: 8px; padding: 20px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-rocket text-primary"></i> إعداد سريع للمشروع</h1>
            <p class="text-muted">إعداد تلقائي وسريع لمنصة ExRayan</p>
        </div>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>ما سيتم عمله:</h5>
            <ul class="mb-0">
                <li>حذف قاعدة البيانات الحالية وإنشاؤها من جديد</li>
                <li>إنشاء الجداول الأساسية فقط</li>
                <li>إنشاء حساب مدير جديد</li>
                <li>إضافة أقسام وإعدادات أساسية</li>
                <li>تجهيز المشروع للاستخدام</li>
            </ul>
        </div>
        
        <div class="text-center">
            <button class="btn btn-setup" onclick="setupProject()">
                <i class="fas fa-rocket me-2"></i>
                بدء الإعداد السريع
            </button>
        </div>
        
        <div id="result"></div>
        
        <div class="text-center mt-4">
            <a href="welcome.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>
    
    <script>
        function setupProject() {
            if (!confirm('هل تريد إعداد المشروع من جديد؟\n\nسيتم حذف جميع البيانات الحالية!')) {
                return;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري إعداد المشروع...</div>';
            
            fetch('setup.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>${data.message}</h5>
                            </div>
                            <div class="credentials-box">
                                <h5 class="text-success"><i class="fas fa-key me-2"></i>بيانات دخول المدير:</h5>
                                <p><strong>اسم المستخدم:</strong> ${data.admin_credentials.username}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${data.admin_credentials.email}</p>
                                <p><strong>كلمة المرور:</strong> ${data.admin_credentials.password}</p>
                                <hr>
                                <div class="text-center">
                                    <a href="/" class="btn btn-primary me-2">
                                        <i class="fas fa-home me-2"></i>
                                        الصفحة الرئيسية
                                    </a>
                                    <a href="admin/" class="btn btn-success">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        لوحة التحكم
                                    </a>
                                </div>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>خطأ: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في تحليل الاستجابة</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
