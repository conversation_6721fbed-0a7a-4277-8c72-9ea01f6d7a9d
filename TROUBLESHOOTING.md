# 🔧 دليل حل المشاكل - ExRayan Platform

## 🚨 المشاكل الشائعة وحلولها

### ❌ **مشكلة: Internal Server Error (500)**

#### الأسباب المحتملة:
1. خطأ في ملف `.htaccess`
2. مشكلة في صلاحيات الملفات
3. خطأ في كود PHP
4. مشكلة في إعدادات Apache

#### الحلول:
```bash
# 1. تحقق من ملف .htaccess
# احذف أو أعد تسمية ملف .htaccess مؤقتاً
mv .htaccess .htaccess.backup

# 2. تحقق من صلاحيات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 .env

# 3. تحقق من سجل الأخطاء
tail -f logs/app_errors.log
```

---

### ❌ **مشكلة: الصفحة الرئيسية لا تعمل**

#### الحلول:
1. **تحقق من وجود قاعدة البيانات:**
   ```
   http://localhost/exrayan/test_connection.php
   ```

2. **استخدم صفحة الترحيب:**
   ```
   http://localhost/exrayan/welcome.php
   ```

3. **فحص حالة النظام:**
   ```
   http://localhost/exrayan/status.php
   ```

---

### ❌ **مشكلة: ملف التثبيت لا يكمل**

#### الأسباب:
- مشكلة في الاتصال بقاعدة البيانات
- ملف `database_schema.sql` غير موجود
- مشكلة في صلاحيات الكتابة

#### الحلول:
1. **تحقق من قاعدة البيانات:**
   ```sql
   -- في phpMyAdmin أو MySQL
   CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **تحقق من وجود الملفات:**
   ```bash
   ls -la database_schema.sql
   ls -la .env
   ```

3. **استخدم زر "تخطي والمتابعة"** في خطوة قاعدة البيانات

---

### ❌ **مشكلة: لا يمكن الوصول للوحة التحكم**

#### الحلول:
1. **إنشاء حساب مدير:**
   ```
   http://localhost/exrayan/create_admin.php
   ```

2. **تحقق من بيانات الدخول:**
   - اسم المستخدم: admin
   - كلمة المرور: admin123 (أو ما أدخلته)

3. **تحقق من قاعدة البيانات:**
   ```sql
   SELECT * FROM users WHERE classification = 'admin';
   ```

---

### ❌ **مشكلة: خطأ في قاعدة البيانات**

#### الحلول:
1. **تحقق من إعدادات .env:**
   ```env
   DB_HOST=127.0.0.1
   DB_DATABASE=exrayan
   DB_USERNAME=root
   DB_PASSWORD=000
   ```

2. **تحقق من تشغيل MySQL:**
   ```bash
   # في XAMPP
   # تأكد من تشغيل Apache و MySQL
   ```

3. **اختبار الاتصال:**
   ```
   http://localhost/exrayan/test_connection.php
   ```

---

## 🛠️ أدوات التشخيص

### 📊 **فحص حالة النظام:**
```
http://localhost/exrayan/status.php
```

### 🔍 **اختبار قاعدة البيانات:**
```
http://localhost/exrayan/test_connection.php
```

### 🏠 **صفحة الترحيب:**
```
http://localhost/exrayan/welcome.php
```

### 📝 **سجلات الأخطاء:**
```
logs/app_errors.log
logs/admin_errors.log
```

---

## 🔧 إعادة التثبيت

### إذا كنت تريد إعادة تثبيت النظام:

1. **احذف ملف التثبيت:**
   ```bash
   rm .installed
   ```

2. **امسح قاعدة البيانات:**
   ```sql
   DROP DATABASE exrayan;
   CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **ابدأ التثبيت من جديد:**
   ```
   http://localhost/exrayan/install.php
   ```

---

## 📞 الحصول على المساعدة

### 🔍 **خطوات التشخيص:**
1. تحقق من `status.php`
2. راجع ملفات السجلات
3. اختبر قاعدة البيانات
4. تحقق من صلاحيات الملفات

### 📧 **التواصل:**
- البريد الإلكتروني: <EMAIL>
- الوثائق: docs.exrayan.com
- GitHub: github.com/exrayan/issues

---

## ✅ قائمة التحقق السريعة

- [ ] تم تشغيل XAMPP/WAMP
- [ ] تم إنشاء قاعدة البيانات `exrayan`
- [ ] ملف `.env` موجود ومحدث
- [ ] ملف `database_schema.sql` موجود
- [ ] المجلدات لها صلاحيات صحيحة
- [ ] لا توجد أخطاء في `logs/app_errors.log`
- [ ] `status.php` يظهر حالة جيدة
- [ ] يمكن الوصول لصفحة الترحيب

---

## 🎯 نصائح مهمة

1. **استخدم دائماً أدوات التشخيص** قبل طلب المساعدة
2. **احتفظ بنسخة احتياطية** من قاعدة البيانات
3. **راجع ملفات السجلات** للحصول على تفاصيل الأخطاء
4. **تأكد من تحديث المتصفح** وإفراغ الذاكرة المؤقتة
5. **استخدم وضع التطوير** لرؤية الأخطاء بوضوح
