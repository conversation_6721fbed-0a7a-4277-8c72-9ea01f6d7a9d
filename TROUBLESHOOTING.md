# 🔧 دليل حل المشاكل - ExRayan Platform

## 🚨 المشاكل الشائعة وحلولها

### ❌ **مشكلة: Internal Server Error (500)**

#### الأسباب المحتملة:
1. خطأ في ملف `.htaccess`
2. مشكلة في صلاحيات الملفات
3. خطأ في كود PHP
4. مشكلة في إعدادات Apache

#### الحلول:
```bash
# 1. تحقق من ملف .htaccess
# احذف أو أعد تسمية ملف .htaccess مؤقتاً
mv .htaccess .htaccess.backup

# 2. تحقق من صلاحيات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 .env

# 3. تحقق من سجل الأخطاء
tail -f logs/app_errors.log
```

---

### ❌ **مشكلة: الصفحة الرئيسية لا تعمل**

#### الحلول:
1. **تحقق من وجود قاعدة البيانات:**
   ```
   http://localhost/exrayan/test_connection.php
   ```

2. **استخدم صفحة الترحيب:**
   ```
   http://localhost/exrayan/welcome.php
   ```

3. **فحص حالة النظام:**
   ```
   http://localhost/exrayan/status.php
   ```

---

### ❌ **مشكلة: ملف التثبيت لا يكمل**

#### الأسباب:
- مشكلة في الاتصال بقاعدة البيانات
- ملف `database_schema.sql` غير موجود
- مشكلة في صلاحيات الكتابة
- **الجداول موجودة مسبقاً (خطأ 1050)**

#### الحلول:
1. **للجداول الموجودة مسبقاً:**
   ```
   استخدم أداة إعادة التعيين: reset_database.php
   ```

2. **تحقق من قاعدة البيانات:**
   ```sql
   -- في phpMyAdmin أو MySQL
   CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **تحقق من وجود الملفات:**
   ```bash
   ls -la database_schema.sql
   ls -la .env
   ```

4. **استخدم زر "تخطي والمتابعة"** في خطوة قاعدة البيانات

### ❌ **مشكلة: خطأ "Table already exists" (1050)**

#### الحل السريع:
```
http://localhost/exrayan/reset_database.php
```
- اختر "إعادة تعيين كاملة" لحذف قاعدة البيانات وإنشاؤها من جديد
- أو اختر "حذف الجداول فقط" للاحتفاظ بقاعدة البيانات

#### الحل اليدوي:
```sql
-- حذف جميع الجداول
DROP DATABASE exrayan;
CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

---

### ❌ **مشكلة: "ليس لديك صلاحية للوصول لهذه الصفحة"**

#### الأسباب:
- تصنيف المستخدم ليس 'admin' أو 'moderator'
- مشكلة في بيانات المستخدم في قاعدة البيانات
- خطأ في إنشاء حساب المدير

#### الحلول:
1. **أداة إصلاح الصلاحيات:**
   ```
   http://localhost/exrayan/fix_admin_user.php
   ```

2. **إنشاء حساب مدير جديد:**
   ```
   http://localhost/exrayan/create_admin.php
   ```

3. **تحقق من قاعدة البيانات:**
   ```sql
   SELECT id, username, email, classification FROM users WHERE classification = 'admin';
   UPDATE users SET classification = 'admin' WHERE id = 1;
   ```

### ❌ **مشكلة: الصفحة الرئيسية تعطي خطأ 404**

#### الأسباب:
- مشكلة في كنترولر home
- ملفات مفقودة
- خطأ في قاعدة البيانات

#### الحلول:
1. **تم إنشاء صفحة رئيسية بديلة** تعمل تلقائياً
2. **تحقق من وجود الملفات:**
   ```
   controllers/home.php
   views/home/<USER>
   ```

3. **اختبار الصفحة الرئيسية:**
   ```
   http://localhost/exrayan/
   ```

---

### ❌ **مشكلة: خطأ في قاعدة البيانات**

#### الحلول:
1. **تحقق من إعدادات .env:**
   ```env
   DB_HOST=127.0.0.1
   DB_DATABASE=exrayan
   DB_USERNAME=root
   DB_PASSWORD=000
   ```

2. **تحقق من تشغيل MySQL:**
   ```bash
   # في XAMPP
   # تأكد من تشغيل Apache و MySQL
   ```

3. **اختبار الاتصال:**
   ```
   http://localhost/exrayan/test_connection.php
   ```

---

## 🛠️ أدوات التشخيص

### 📊 **فحص حالة النظام:**
```
http://localhost/exrayan/status.php
```

### 🔍 **اختبار قاعدة البيانات:**
```
http://localhost/exrayan/test_connection.php
```

### 🏠 **صفحة الترحيب:**
```
http://localhost/exrayan/welcome.php
```

### 🔄 **إعادة تعيين قاعدة البيانات:**
```
http://localhost/exrayan/reset_database.php
```

### 📋 **فحص حالة التثبيت:**
```
http://localhost/exrayan/check_installation.php
```

### 🛡️ **إصلاح صلاحيات المدير:**
```
http://localhost/exrayan/fix_admin_user.php
```

### 📝 **سجلات الأخطاء:**
```
logs/app_errors.log
logs/admin_errors.log
```

---

## 🔧 إعادة التثبيت

### إذا كنت تريد إعادة تثبيت النظام:

1. **احذف ملف التثبيت:**
   ```bash
   rm .installed
   ```

2. **امسح قاعدة البيانات:**
   ```sql
   DROP DATABASE exrayan;
   CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **ابدأ التثبيت من جديد:**
   ```
   http://localhost/exrayan/install.php
   ```

---

## 📞 الحصول على المساعدة

### 🔍 **خطوات التشخيص:**
1. تحقق من `status.php`
2. راجع ملفات السجلات
3. اختبر قاعدة البيانات
4. تحقق من صلاحيات الملفات

### 📧 **التواصل:**
- البريد الإلكتروني: <EMAIL>
- الوثائق: docs.exrayan.com
- GitHub: github.com/exrayan/issues

---

## ✅ قائمة التحقق السريعة

- [ ] تم تشغيل XAMPP/WAMP
- [ ] تم إنشاء قاعدة البيانات `exrayan`
- [ ] ملف `.env` موجود ومحدث
- [ ] ملف `database_schema.sql` موجود
- [ ] المجلدات لها صلاحيات صحيحة
- [ ] لا توجد أخطاء في `logs/app_errors.log`
- [ ] `status.php` يظهر حالة جيدة
- [ ] يمكن الوصول لصفحة الترحيب

---

## 🎯 نصائح مهمة

1. **استخدم دائماً أدوات التشخيص** قبل طلب المساعدة
2. **احتفظ بنسخة احتياطية** من قاعدة البيانات
3. **راجع ملفات السجلات** للحصول على تفاصيل الأخطاء
4. **تأكد من تحديث المتصفح** وإفراغ الذاكرة المؤقتة
5. **استخدم وضع التطوير** لرؤية الأخطاء بوضوح
