<?php
/**
 * كنترولر لوحة التحكم الرئيسية - ExRayan Platform
 * Dashboard Controller
 */

class DashboardController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الصفحة الرئيسية للوحة التحكم
     */
    public function index() {
        try {
            // جلب الإحصائيات العامة
            $stats = $this->getGeneralStats();
            
            // جلب الإحصائيات الشهرية
            $monthlyStats = $this->getMonthlyStats();
            
            // جلب أحدث الإعلانات
            $latestAds = $this->getLatestAds();
            
            // جلب أحدث المستخدمين
            $latestUsers = $this->getLatestUsers();
            
            // جلب الأنشطة الأخيرة
            $recentActivities = $this->getRecentActivities();
            
            // جلب التنبيهات
            $alerts = $this->getSystemAlerts();
            
            // تمرير البيانات للعرض
            $data = [
                'title' => 'لوحة التحكم الرئيسية',
                'stats' => $stats,
                'monthly_stats' => $monthlyStats,
                'latest_ads' => $latestAds,
                'latest_users' => $latestUsers,
                'recent_activities' => $recentActivities,
                'alerts' => $alerts,
                'user' => Session::getUser()
            ];
            
            $this->render('dashboard/index', $data);
            
        } catch (Exception $e) {
            error_log("Dashboard Controller Error: " . $e->getMessage());
            $this->render('errors/500');
        }
    }
    
    /**
     * جلب الإحصائيات العامة
     */
    private function getGeneralStats() {
        $stats = [];
        
        // عدد الإعلانات
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads");
        $stats['total_ads'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads WHERE status = 'active'");
        $stats['active_ads'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'");
        $stats['pending_ads'] = $result['count'] ?? 0;
        
        // عدد المستخدمين
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM users");
        $stats['total_users'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stats['active_users'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
        $stats['new_users_today'] = $result['count'] ?? 0;
        
        // عدد المتاجر
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM stores");
        $stats['total_stores'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM stores WHERE status = 'active'");
        $stats['active_stores'] = $result['count'] ?? 0;
        
        // عدد المزادات
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM auctions");
        $stats['total_auctions'] = $result['count'] ?? 0;
        
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM auctions WHERE status = 'active'");
        $stats['active_auctions'] = $result['count'] ?? 0;
        
        // الإيرادات
        $result = $this->db->selectOne("SELECT SUM(amount) as total FROM transactions WHERE type = 'commission' AND status = 'approved'");
        $stats['total_revenue'] = $result['total'] ?? 0;
        
        $result = $this->db->selectOne("SELECT SUM(amount) as total FROM transactions WHERE type = 'commission' AND status = 'approved' AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
        $stats['monthly_revenue'] = $result['total'] ?? 0;
        
        return $stats;
    }
    
    /**
     * جلب الإحصائيات الشهرية
     */
    private function getMonthlyStats() {
        $monthlyData = [];
        
        // إحصائيات آخر 12 شهر
        for ($i = 11; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-{$i} months"));
            $monthName = date('M Y', strtotime("-{$i} months"));
            
            // عدد الإعلانات الجديدة
            $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads WHERE DATE_FORMAT(created_at, '%Y-%m') = ?", [$month]);
            $adsCount = $result['count'] ?? 0;
            
            // عدد المستخدمين الجدد
            $result = $this->db->selectOne("SELECT COUNT(*) as count FROM users WHERE DATE_FORMAT(created_at, '%Y-%m') = ?", [$month]);
            $usersCount = $result['count'] ?? 0;
            
            // الإيرادات
            $result = $this->db->selectOne("SELECT SUM(amount) as total FROM transactions WHERE type = 'commission' AND status = 'approved' AND DATE_FORMAT(created_at, '%Y-%m') = ?", [$month]);
            $revenue = $result['total'] ?? 0;
            
            $monthlyData[] = [
                'month' => $monthName,
                'ads' => $adsCount,
                'users' => $usersCount,
                'revenue' => $revenue
            ];
        }
        
        return $monthlyData;
    }
    
    /**
     * جلب أحدث الإعلانات
     */
    private function getLatestAds() {
        return $this->db->select("
            SELECT ads.*, users.username, sections.name as section_name,
                   (SELECT image_path FROM ad_images WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM ads 
            LEFT JOIN users ON ads.user_id = users.id
            LEFT JOIN sections ON ads.section_id = sections.id
            ORDER BY ads.created_at DESC
            LIMIT 10
        ");
    }
    
    /**
     * جلب أحدث المستخدمين
     */
    private function getLatestUsers() {
        return $this->db->select("
            SELECT users.*, countries.name as country_name, cities.name as city_name
            FROM users 
            LEFT JOIN countries ON users.country_id = countries.id
            LEFT JOIN cities ON users.city_id = cities.id
            ORDER BY users.created_at DESC
            LIMIT 10
        ");
    }
    
    /**
     * جلب الأنشطة الأخيرة
     */
    private function getRecentActivities() {
        return $this->db->select("
            SELECT activity_logs.*, users.username
            FROM activity_logs 
            LEFT JOIN users ON activity_logs.user_id = users.id
            ORDER BY activity_logs.created_at DESC
            LIMIT 20
        ");
    }
    
    /**
     * جلب التنبيهات النظام
     */
    private function getSystemAlerts() {
        $alerts = [];
        
        // تحقق من الإعلانات المعلقة
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'");
        $pendingAds = $result['count'] ?? 0;
        if ($pendingAds > 0) {
            $alerts[] = [
                'type' => 'warning',
                'icon' => 'fas fa-clock',
                'title' => 'إعلانات معلقة',
                'message' => "يوجد {$pendingAds} إعلان بانتظار المراجعة",
                'url' => 'ads?status=pending'
            ];
        }
        
        // تحقق من الشكاوى الجديدة
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM complaints WHERE status = 'new'");
        $newComplaints = $result['count'] ?? 0;
        if ($newComplaints > 0) {
            $alerts[] = [
                'type' => 'danger',
                'icon' => 'fas fa-exclamation-triangle',
                'title' => 'شكاوى جديدة',
                'message' => "يوجد {$newComplaints} شكوى جديدة تحتاج للمراجعة",
                'url' => 'complaints?status=new'
            ];
        }
        
        // تحقق من النشاطات المشبوهة
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM activity_logs WHERE result = 'blocked' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $suspiciousActivities = $result['count'] ?? 0;
        if ($suspiciousActivities > 10) {
            $alerts[] = [
                'type' => 'danger',
                'icon' => 'fas fa-shield-alt',
                'title' => 'نشاطات مشبوهة',
                'message' => "تم رصد {$suspiciousActivities} نشاط مشبوه خلال آخر 24 ساعة",
                'url' => 'security/activities'
            ];
        }
        
        // تحقق من مساحة التخزين
        $uploadsPath = __DIR__ . '/../../uploads';
        if (is_dir($uploadsPath)) {
            $size = $this->getDirSize($uploadsPath);
            $sizeMB = round($size / 1024 / 1024, 2);
            if ($sizeMB > 1000) { // أكثر من 1 جيجا
                $alerts[] = [
                    'type' => 'info',
                    'icon' => 'fas fa-hdd',
                    'title' => 'مساحة التخزين',
                    'message' => "مجلد الرفع يستخدم {$sizeMB} ميجابايت",
                    'url' => 'system/storage'
                ];
            }
        }
        
        return $alerts;
    }
    
    /**
     * حساب حجم المجلد
     */
    private function getDirSize($directory) {
        $size = 0;
        if (is_dir($directory)) {
            foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory)) as $file) {
                $size += $file->getSize();
            }
        }
        return $size;
    }
    
    /**
     * عرض القالب
     */
    private function render($view, $data = []) {
        // استخراج المتغيرات
        extract($data);
        
        // تحديد مسار القالب
        $viewFile = __DIR__ . "/../views/{$view}.php";
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: {$view}");
        }
    }
}
