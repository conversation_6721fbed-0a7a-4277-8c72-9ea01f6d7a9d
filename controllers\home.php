<?php
/**
 * كنترولر الصفحة الرئيسية - ExRayan Platform
 * Home Controller
 */

class HomeController {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الصفحة الرئيسية
     */
    public function index() {
        try {
            // جلب الإعدادات العامة
            $settings = $this->getSettings();
            
            // جلب الأقسام الرئيسية
            $sections = $this->getMainSections();
            
            // جلب الإعلانات المميزة
            $featuredAds = $this->getFeaturedAds();
            
            // جلب أحدث الإعلانات
            $latestAds = $this->getLatestAds();
            
            // جلب إحصائيات الموقع
            $stats = $this->getSiteStats();
            
            // تمرير البيانات للعرض
            $data = [
                'title' => $settings['site_name'] ?? 'ExRayan',
                'description' => $settings['site_description'] ?? 'منصة الإعلانات المبوبة',
                'sections' => $sections,
                'featured_ads' => $featuredAds,
                'latest_ads' => $latestAds,
                'stats' => $stats,
                'user' => Session::getUser()
            ];
            
            $this->render('home/index', $data);
            
        } catch (Exception $e) {
            error_log("Home Controller Error: " . $e->getMessage());
            $this->render('errors/500');
        }
    }
    
    /**
     * صفحة البحث
     */
    public function search() {
        $query = Security::sanitizeInput($_GET['q'] ?? '');
        $section = Security::sanitizeInput($_GET['section'] ?? '');
        $city = Security::sanitizeInput($_GET['city'] ?? '');
        $minPrice = floatval($_GET['min_price'] ?? 0);
        $maxPrice = floatval($_GET['max_price'] ?? 0);
        $page = intval($_GET['page'] ?? 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        try {
            // بناء استعلام البحث
            $whereConditions = ["ads.status = 'active'"];
            $params = [];
            
            if (!empty($query)) {
                $whereConditions[] = "(ads.title LIKE :query OR ads.description LIKE :query)";
                $params['query'] = "%{$query}%";
            }
            
            if (!empty($section)) {
                $whereConditions[] = "ads.section_id = :section";
                $params['section'] = $section;
            }
            
            if (!empty($city)) {
                $whereConditions[] = "ads.city_id = :city";
                $params['city'] = $city;
            }
            
            if ($minPrice > 0) {
                $whereConditions[] = "ads.price >= :min_price";
                $params['min_price'] = $minPrice;
            }
            
            if ($maxPrice > 0) {
                $whereConditions[] = "ads.price <= :max_price";
                $params['max_price'] = $maxPrice;
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // جلب النتائج
            $sql = "SELECT ads.*, users.username, cities.name as city_name, sections.name as section_name,
                           (SELECT image_path FROM ad_images WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_image
                    FROM ads 
                    LEFT JOIN users ON ads.user_id = users.id
                    LEFT JOIN cities ON ads.city_id = cities.id
                    LEFT JOIN sections ON ads.section_id = sections.id
                    WHERE {$whereClause}
                    ORDER BY ads.is_featured DESC, ads.is_pinned DESC, ads.created_at DESC
                    LIMIT :limit OFFSET :offset";
            
            $params['limit'] = $limit;
            $params['offset'] = $offset;
            
            $results = $this->db->select($sql, $params);
            
            // عدد النتائج الإجمالي
            $countSql = "SELECT COUNT(*) as total FROM ads WHERE {$whereClause}";
            unset($params['limit'], $params['offset']);
            $totalResult = $this->db->selectOne($countSql, $params);
            $total = $totalResult['total'] ?? 0;
            
            // حساب الصفحات
            $totalPages = ceil($total / $limit);
            
            $data = [
                'title' => 'نتائج البحث - ExRayan',
                'query' => $query,
                'results' => $results,
                'total' => $total,
                'page' => $page,
                'total_pages' => $totalPages,
                'sections' => $this->getMainSections(),
                'cities' => $this->getCities()
            ];
            
            $this->render('home/search', $data);
            
        } catch (Exception $e) {
            error_log("Search Error: " . $e->getMessage());
            $this->render('errors/500');
        }
    }
    
    /**
     * جلب الإعدادات العامة
     */
    private function getSettings() {
        $settings = [];
        $results = $this->db->select("SELECT setting_key, setting_value FROM settings WHERE is_public = 1");
        
        foreach ($results as $setting) {
            $settings[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * جلب الأقسام الرئيسية
     */
    private function getMainSections() {
        return $this->db->select("
            SELECT id, name, slug, description, image, icon,
                   (SELECT COUNT(*) FROM ads WHERE section_id = sections.id AND status = 'active') as ads_count
            FROM sections 
            WHERE parent_id IS NULL AND is_active = 1 AND show_in_homepage = 1
            ORDER BY sort_order ASC
        ");
    }
    
    /**
     * جلب الإعلانات المميزة
     */
    private function getFeaturedAds() {
        return $this->db->select("
            SELECT ads.*, users.username, cities.name as city_name,
                   (SELECT image_path FROM ad_images WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM ads 
            LEFT JOIN users ON ads.user_id = users.id
            LEFT JOIN cities ON ads.city_id = cities.id
            WHERE ads.status = 'active' AND ads.is_featured = 1 
            AND (ads.featured_expires_at IS NULL OR ads.featured_expires_at > NOW())
            ORDER BY ads.created_at DESC
            LIMIT 8
        ");
    }
    
    /**
     * جلب أحدث الإعلانات
     */
    private function getLatestAds() {
        return $this->db->select("
            SELECT ads.*, users.username, cities.name as city_name, sections.name as section_name,
                   (SELECT image_path FROM ad_images WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_image
            FROM ads 
            LEFT JOIN users ON ads.user_id = users.id
            LEFT JOIN cities ON ads.city_id = cities.id
            LEFT JOIN sections ON ads.section_id = sections.id
            WHERE ads.status = 'active'
            ORDER BY ads.created_at DESC
            LIMIT 12
        ");
    }
    
    /**
     * جلب إحصائيات الموقع
     */
    private function getSiteStats() {
        $stats = [];
        
        // عدد الإعلانات النشطة
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM ads WHERE status = 'active'");
        $stats['active_ads'] = $result['count'] ?? 0;
        
        // عدد المستخدمين
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stats['users'] = $result['count'] ?? 0;
        
        // عدد المتاجر
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM stores WHERE status = 'active'");
        $stats['stores'] = $result['count'] ?? 0;
        
        // عدد المزادات النشطة
        $result = $this->db->selectOne("SELECT COUNT(*) as count FROM auctions WHERE status = 'active'");
        $stats['auctions'] = $result['count'] ?? 0;
        
        return $stats;
    }
    
    /**
     * جلب المدن
     */
    private function getCities() {
        return $this->db->select("
            SELECT id, name,
                   (SELECT COUNT(*) FROM ads WHERE city_id = cities.id AND status = 'active') as ads_count
            FROM cities 
            WHERE is_active = 1
            ORDER BY name ASC
        ");
    }
    
    /**
     * عرض القالب
     */
    private function render($view, $data = []) {
        // استخراج المتغيرات
        extract($data);
        
        // تحديد مسار القالب
        $viewFile = __DIR__ . "/../views/{$view}.php";
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: {$view}");
        }
    }
}
