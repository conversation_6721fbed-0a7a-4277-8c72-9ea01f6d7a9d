<?php
/**
 * صفحة حالة النظام - ExRayan Platform
 * System Status Page
 */

header('Content-Type: application/json; charset=UTF-8');

$status = [
    'system' => 'ExRayan Platform',
    'version' => '1.0.0',
    'timestamp' => date('Y-m-d H:i:s'),
    'status' => 'checking',
    'checks' => []
];

try {
    // فحص PHP
    $status['checks']['php'] = [
        'name' => 'PHP Version',
        'status' => version_compare(PHP_VERSION, '7.4.0', '>=') ? 'ok' : 'error',
        'value' => PHP_VERSION,
        'required' => '7.4+'
    ];
    
    // فحص الإضافات
    $extensions = ['mysqli', 'pdo_mysql', 'json', 'openssl', 'mbstring'];
    foreach ($extensions as $ext) {
        $status['checks']['ext_' . $ext] = [
            'name' => $ext . ' Extension',
            'status' => extension_loaded($ext) ? 'ok' : 'error',
            'value' => extension_loaded($ext) ? 'loaded' : 'not loaded'
        ];
    }
    
    // فحص المجلدات
    $directories = ['uploads', 'logs', 'backups'];
    foreach ($directories as $dir) {
        $path = __DIR__ . '/' . $dir;
        $exists = is_dir($path);
        $writable = $exists && is_writable($path);
        
        $status['checks']['dir_' . $dir] = [
            'name' => $dir . ' Directory',
            'status' => ($exists && $writable) ? 'ok' : 'warning',
            'value' => $exists ? ($writable ? 'writable' : 'not writable') : 'not exists'
        ];
    }
    
    // فحص الملفات المهمة
    $files = ['.env', 'database_schema.sql', 'install.php'];
    foreach ($files as $file) {
        $exists = file_exists(__DIR__ . '/' . $file);
        $status['checks']['file_' . str_replace('.', '_', $file)] = [
            'name' => $file . ' File',
            'status' => $exists ? 'ok' : 'warning',
            'value' => $exists ? 'exists' : 'not exists'
        ];
    }
    
    // فحص قاعدة البيانات
    try {
        if (file_exists(__DIR__ . '/.env')) {
            $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value, '"\'');
                }
            }
        }
        
        require_once __DIR__ . '/includes/Database.php';
        $db = Database::getInstance();
        $result = $db->selectOne("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?", [$_ENV['DB_DATABASE'] ?? 'exrayan']);
        
        $status['checks']['database'] = [
            'name' => 'Database Connection',
            'status' => 'ok',
            'value' => 'connected (' . ($result['count'] ?? 0) . ' tables)'
        ];
        
    } catch (Exception $e) {
        $status['checks']['database'] = [
            'name' => 'Database Connection',
            'status' => 'error',
            'value' => 'failed: ' . $e->getMessage()
        ];
    }
    
    // فحص التثبيت
    $installed = file_exists(__DIR__ . '/.installed');
    $status['checks']['installation'] = [
        'name' => 'Installation Status',
        'status' => $installed ? 'ok' : 'warning',
        'value' => $installed ? 'installed' : 'not installed'
    ];
    
    // تحديد الحالة العامة
    $hasErrors = false;
    $hasWarnings = false;
    
    foreach ($status['checks'] as $check) {
        if ($check['status'] === 'error') {
            $hasErrors = true;
            break;
        } elseif ($check['status'] === 'warning') {
            $hasWarnings = true;
        }
    }
    
    if ($hasErrors) {
        $status['status'] = 'error';
        $status['message'] = 'System has critical errors';
    } elseif ($hasWarnings) {
        $status['status'] = 'warning';
        $status['message'] = 'System has warnings';
    } else {
        $status['status'] = 'ok';
        $status['message'] = 'All systems operational';
    }
    
} catch (Exception $e) {
    $status['status'] = 'error';
    $status['message'] = 'System check failed: ' . $e->getMessage();
}

// إرجاع النتيجة
echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
