<?php
/**
 * اختبار النظام الشامل - ExRayan Platform
 * Comprehensive System Test
 */

header('Content-Type: text/html; charset=UTF-8');

$tests = [
    'files' => [],
    'database' => [],
    'admin' => [],
    'pages' => []
];

$results = [
    'passed' => 0,
    'failed' => 0,
    'warnings' => 0
];

// اختبار الملفات المطلوبة
$requiredFiles = [
    'index.php' => 'الملف الرئيسي',
    'admin/index.php' => 'لوحة التحكم',
    'admin/login.php' => 'تسجيل دخول الإدارة',
    'views/home/<USER>' => 'الصفحة الرئيسية البديلة',
    'fix_admin_user.php' => 'أداة إصلاح الصلاحيات',
    'reset_database.php' => 'أداة إعادة التعيين',
    'diagnose.php' => 'أداة التشخيص',
    'welcome.php' => 'صفحة الترحيب'
];

foreach ($requiredFiles as $file => $description) {
    $exists = file_exists($file);
    $tests['files'][] = [
        'name' => $description,
        'file' => $file,
        'status' => $exists ? 'pass' : 'fail',
        'message' => $exists ? 'موجود' : 'غير موجود'
    ];
    
    if ($exists) {
        $results['passed']++;
    } else {
        $results['failed']++;
    }
}

// اختبار قاعدة البيانات
try {
    // تحميل متغيرات البيئة
    if (file_exists('.env')) {
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value, '"\'');
            }
        }
    }
    
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '000';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $tests['database'][] = [
        'name' => 'اتصال قاعدة البيانات',
        'status' => 'pass',
        'message' => 'متصل بنجاح'
    ];
    $results['passed']++;
    
    // فحص الجداول
    $requiredTables = ['users', 'sections', 'ads', 'settings'];
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($requiredTables as $table) {
        $exists = in_array($table, $existingTables);
        $tests['database'][] = [
            'name' => "جدول $table",
            'status' => $exists ? 'pass' : 'fail',
            'message' => $exists ? 'موجود' : 'غير موجود'
        ];
        
        if ($exists) {
            $results['passed']++;
        } else {
            $results['failed']++;
        }
    }
    
    // فحص المدير
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE classification = 'admin'");
    $stmt->execute();
    $result = $stmt->fetch();
    $adminCount = $result['count'];
    
    $tests['admin'][] = [
        'name' => 'حساب المدير',
        'status' => $adminCount > 0 ? 'pass' : 'warning',
        'message' => $adminCount > 0 ? "يوجد $adminCount مدير" : 'لا يوجد مدير'
    ];
    
    if ($adminCount > 0) {
        $results['passed']++;
    } else {
        $results['warnings']++;
    }
    
} catch (Exception $e) {
    $tests['database'][] = [
        'name' => 'اتصال قاعدة البيانات',
        'status' => 'fail',
        'message' => $e->getMessage()
    ];
    $results['failed']++;
}

// اختبار الصفحات
$pages = [
    '/' => 'الصفحة الرئيسية',
    '/welcome.php' => 'صفحة الترحيب',
    '/admin/login.php' => 'تسجيل دخول الإدارة'
];

foreach ($pages as $url => $name) {
    // محاولة الوصول للصفحة
    $fullUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . $url;
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($fullUrl, false, $context);
    $httpCode = 200;
    
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $httpCode = (int) substr($header, 9, 3);
                break;
            }
        }
    }
    
    $tests['pages'][] = [
        'name' => $name,
        'url' => $url,
        'status' => $httpCode === 200 ? 'pass' : ($httpCode === 404 ? 'fail' : 'warning'),
        'message' => "HTTP $httpCode"
    ];
    
    if ($httpCode === 200) {
        $results['passed']++;
    } elseif ($httpCode === 404) {
        $results['failed']++;
    } else {
        $results['warnings']++;
    }
}

$totalTests = $results['passed'] + $results['failed'] + $results['warnings'];
$successRate = $totalTests > 0 ? round(($results['passed'] / $totalTests) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الشامل - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: #f5f7fa; padding: 20px; }
        .test-container { max-width: 1000px; margin: 0 auto; }
        .test-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .progress { height: 25px; }
        .test-item { padding: 8px 0; border-bottom: 1px solid #eee; }
        .test-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-vial text-primary"></i> اختبار النظام الشامل</h1>
            <p class="text-muted">فحص شامل لجميع مكونات ExRayan Platform</p>
        </div>
        
        <!-- ملخص النتائج -->
        <div class="test-card">
            <h3 class="text-center mb-4">ملخص النتائج</h3>
            
            <div class="row text-center mb-4">
                <div class="col-md-3">
                    <div class="status-pass">
                        <i class="fas fa-check-circle fa-2x"></i>
                        <h4><?= $results['passed'] ?></h4>
                        <small>نجح</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-fail">
                        <i class="fas fa-times-circle fa-2x"></i>
                        <h4><?= $results['failed'] ?></h4>
                        <small>فشل</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-warning">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                        <h4><?= $results['warnings'] ?></h4>
                        <small>تحذير</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-info">
                        <i class="fas fa-percentage fa-2x"></i>
                        <h4><?= $successRate ?>%</h4>
                        <small>معدل النجاح</small>
                    </div>
                </div>
            </div>
            
            <div class="progress">
                <div class="progress-bar bg-success" style="width: <?= ($results['passed'] / $totalTests) * 100 ?>%"></div>
                <div class="progress-bar bg-warning" style="width: <?= ($results['warnings'] / $totalTests) * 100 ?>%"></div>
                <div class="progress-bar bg-danger" style="width: <?= ($results['failed'] / $totalTests) * 100 ?>%"></div>
            </div>
        </div>
        
        <!-- اختبار الملفات -->
        <div class="test-card">
            <h4><i class="fas fa-file me-2"></i>اختبار الملفات</h4>
            <?php foreach ($tests['files'] as $test): ?>
            <div class="test-item d-flex justify-content-between align-items-center">
                <span><?= $test['name'] ?> <small class="text-muted">(<?= $test['file'] ?>)</small></span>
                <span class="status-<?= $test['status'] ?>">
                    <i class="fas fa-<?= $test['status'] === 'pass' ? 'check' : 'times' ?>"></i>
                    <?= $test['message'] ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- اختبار قاعدة البيانات -->
        <div class="test-card">
            <h4><i class="fas fa-database me-2"></i>اختبار قاعدة البيانات</h4>
            <?php foreach ($tests['database'] as $test): ?>
            <div class="test-item d-flex justify-content-between align-items-center">
                <span><?= $test['name'] ?></span>
                <span class="status-<?= $test['status'] ?>">
                    <i class="fas fa-<?= $test['status'] === 'pass' ? 'check' : ($test['status'] === 'fail' ? 'times' : 'exclamation-triangle') ?>"></i>
                    <?= $test['message'] ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- اختبار المدير -->
        <div class="test-card">
            <h4><i class="fas fa-user-shield me-2"></i>اختبار المدير</h4>
            <?php foreach ($tests['admin'] as $test): ?>
            <div class="test-item d-flex justify-content-between align-items-center">
                <span><?= $test['name'] ?></span>
                <span class="status-<?= $test['status'] ?>">
                    <i class="fas fa-<?= $test['status'] === 'pass' ? 'check' : ($test['status'] === 'fail' ? 'times' : 'exclamation-triangle') ?>"></i>
                    <?= $test['message'] ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- اختبار الصفحات -->
        <div class="test-card">
            <h4><i class="fas fa-globe me-2"></i>اختبار الصفحات</h4>
            <?php foreach ($tests['pages'] as $test): ?>
            <div class="test-item d-flex justify-content-between align-items-center">
                <span><?= $test['name'] ?> <small class="text-muted">(<?= $test['url'] ?>)</small></span>
                <span class="status-<?= $test['status'] ?>">
                    <i class="fas fa-<?= $test['status'] === 'pass' ? 'check' : ($test['status'] === 'fail' ? 'times' : 'exclamation-triangle') ?>"></i>
                    <?= $test['message'] ?>
                </span>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- الإجراءات المقترحة -->
        <?php if ($results['failed'] > 0 || $results['warnings'] > 0): ?>
        <div class="test-card">
            <h4><i class="fas fa-tools me-2"></i>الإجراءات المقترحة</h4>
            
            <?php if ($results['failed'] > 0): ?>
            <div class="alert alert-danger">
                <h6>مشاكل تحتاج لحل فوري:</h6>
                <ul class="mb-0">
                    <li>استخدم <a href="diagnose.php">أداة التشخيص الشاملة</a> لمزيد من التفاصيل</li>
                    <li>تحقق من <a href="reset_database.php">إعادة تعيين قاعدة البيانات</a> إذا كانت المشكلة في الجداول</li>
                    <li>راجع <a href="TROUBLESHOOTING.md">دليل حل المشاكل</a></li>
                </ul>
            </div>
            <?php endif; ?>
            
            <?php if ($results['warnings'] > 0): ?>
            <div class="alert alert-warning">
                <h6>تحذيرات تحتاج لانتباه:</h6>
                <ul class="mb-0">
                    <li>استخدم <a href="fix_admin_user.php">أداة إصلاح الصلاحيات</a> لحل مشاكل المدير</li>
                    <li>تحقق من <a href="check_installation.php">حالة التثبيت</a></li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div class="text-center">
            <a href="welcome.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>
            <a href="diagnose.php" class="btn btn-info">تشخيص مفصل</a>
            <button class="btn btn-secondary" onclick="location.reload()">إعادة الاختبار</button>
        </div>
    </div>
</body>
</html>
