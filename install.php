<?php
/**
 * ملف التثبيت السريع - ExRayan Platform
 * Quick Installation Script
 */

// معالجة طلبات AJAX أولاً
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');

    $action = $_POST['action'] ?? '';

    if ($action === 'test_connection') {
        try {
            $host = $_POST['db_host'] ?? '127.0.0.1';
            $dbname = $_POST['db_name'] ?? 'exrayan';
            $username = $_POST['db_user'] ?? 'root';
            $password = $_POST['db_pass'] ?? '000';

            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            echo json_encode(['success' => true, 'message' => 'تم الاتصال بنجاح']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }

    if ($action === 'install') {
        try {
            // الاتصال بقاعدة البيانات
            $host = $_POST['db_host'] ?? '127.0.0.1';
            $dbname = $_POST['db_name'] ?? 'exrayan';
            $username = $_POST['db_user'] ?? 'root';
            $password = $_POST['db_pass'] ?? '000';

            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // تنفيذ ملف قاعدة البيانات
            if (!file_exists('database_schema.sql')) {
                throw new Exception('ملف قاعدة البيانات غير موجود (database_schema.sql)');
            }

            $sql = file_get_contents('database_schema.sql');
            if (empty($sql)) {
                throw new Exception('ملف قاعدة البيانات فارغ');
            }

            // تنفيذ الاستعلامات
            $statements = explode(';', $sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }

            // إنشاء حساب المدير
            $adminUsername = $_POST['admin_username'];
            $adminEmail = $_POST['admin_email'];
            $adminPassword = password_hash($_POST['admin_password'], PASSWORD_ARGON2ID);

            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role_id, classification, is_active, email_verified_at) VALUES (?, ?, ?, 1, 'admin', 1, NOW())");
            $stmt->execute([$adminUsername, $adminEmail, $adminPassword]);

            // تحديث ملف .env
            if (file_exists('.env')) {
                $envContent = file_get_contents('.env');
                $envContent = str_replace('DB_HOST=127.0.0.1', "DB_HOST=$host", $envContent);
                $envContent = str_replace('DB_DATABASE=exrayan', "DB_DATABASE=$dbname", $envContent);
                $envContent = str_replace('DB_USERNAME=root', "DB_USERNAME=$username", $envContent);
                $envContent = str_replace('DB_PASSWORD=000', "DB_PASSWORD=$password", $envContent);
                file_put_contents('.env', $envContent);
            }

            // إنشاء ملف التثبيت
            file_put_contents('.installed', date('Y-m-d H:i:s'));

            echo json_encode(['success' => true, 'message' => 'تم التثبيت بنجاح']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }

    // إذا لم يكن هناك إجراء صحيح
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}

// منع الوصول إذا كان المشروع مثبت مسبقاً
if (file_exists('.installed')) {
    die('المشروع مثبت مسبقاً. احذف ملف .installed لإعادة التثبيت.');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت ExRayan - منصة الإعلانات المبوبة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #666;
            font-size: 1.1em;
        }
        
        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .step.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            margin-left: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .progress {
            background: #f0f0f0;
            height: 6px;
            border-radius: 3px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .progress-bar {
            background: #667eea;
            height: 100%;
            transition: width 0.3s;
            border-radius: 3px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .requirements {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .requirement:last-child {
            border-bottom: none;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>ExRayan</h1>
            <p>منصة الإعلانات المبوبة المتطورة</p>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 25%"></div>
        </div>
        
        <!-- الخطوة 1: فحص المتطلبات -->
        <div class="step active" id="step1">
            <h2>فحص متطلبات النظام</h2>
            <div class="requirements">
                <?php
                $requirements = [
                    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                    'MySQL Extension' => extension_loaded('mysqli') || extension_loaded('pdo_mysql'),
                    'JSON Extension' => extension_loaded('json'),
                    'OpenSSL Extension' => extension_loaded('openssl'),
                    'Uploads Directory Writable' => is_writable(__DIR__ . '/uploads') || mkdir(__DIR__ . '/uploads', 0755, true),
                    'Logs Directory Writable' => is_writable(__DIR__ . '/logs') || mkdir(__DIR__ . '/logs', 0755, true),
                ];
                
                $allPassed = true;
                foreach ($requirements as $requirement => $status) {
                    $statusClass = $status ? 'status-ok' : 'status-error';
                    $statusText = $status ? '✓ متوفر' : '✗ غير متوفر';
                    if (!$status) $allPassed = false;
                    
                    echo "<div class='requirement'>";
                    echo "<span>{$requirement}</span>";
                    echo "<span class='{$statusClass}'>{$statusText}</span>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <?php if ($allPassed): ?>
                <div class="alert alert-success">
                    جميع المتطلبات متوفرة! يمكنك المتابعة للخطوة التالية.
                </div>
                <button class="btn" onclick="nextStep()">التالي</button>
            <?php else: ?>
                <div class="alert alert-error">
                    يرجى التأكد من توفر جميع المتطلبات قبل المتابعة.
                </div>
                <button class="btn" onclick="location.reload()">إعادة الفحص</button>
            <?php endif; ?>
        </div>
        
        <!-- الخطوة 2: إعدادات قاعدة البيانات -->
        <div class="step" id="step2">
            <h2>إعدادات قاعدة البيانات</h2>
            <form id="dbForm">
                <div class="form-group">
                    <label>خادم قاعدة البيانات:</label>
                    <input type="text" name="db_host" value="127.0.0.1" required>
                </div>

                <div class="form-group">
                    <label>اسم قاعدة البيانات:</label>
                    <input type="text" name="db_name" value="exrayan" required>
                </div>

                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" name="db_user" value="root" required>
                </div>

                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" name="db_pass" value="000">
                </div>
                
                <button type="button" class="btn-secondary btn" onclick="prevStep()">السابق</button>
                <button type="button" class="btn" onclick="testConnection()">اختبار الاتصال</button>
                <button type="button" class="btn" onclick="nextStep()" style="margin-right: 10px;">تخطي والمتابعة</button>
            </form>

            <div id="connectionResult"></div>
        </div>
        
        <!-- الخطوة 3: إعداد حساب المدير -->
        <div class="step" id="step3">
            <h2>إنشاء حساب المدير</h2>
            <form id="adminForm">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" name="admin_username" value="admin" required>
                </div>
                
                <div class="form-group">
                    <label>البريد الإلكتروني:</label>
                    <input type="email" name="admin_email" required>
                </div>
                
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" name="admin_password" required>
                </div>
                
                <div class="form-group">
                    <label>تأكيد كلمة المرور:</label>
                    <input type="password" name="admin_password_confirm" required>
                </div>
                
                <button type="button" class="btn-secondary btn" onclick="prevStep()">السابق</button>
                <button type="button" class="btn" onclick="install()">تثبيت النظام</button>
            </form>
        </div>
        
        <!-- الخطوة 4: اكتمال التثبيت -->
        <div class="step" id="step4">
            <h2>تم التثبيت بنجاح!</h2>
            <div class="alert alert-success">
                <p>تم تثبيت منصة ExRayan بنجاح!</p>
                <p>يمكنك الآن الوصول للوحة التحكم باستخدام بيانات المدير التي أدخلتها.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">الذهاب للموقع</a>
                <a href="admin/" class="btn btn-secondary">لوحة التحكم</a>
            </div>
        </div>
    </div>
    
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function showStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById('step' + step).classList.add('active');
            updateProgress();
        }
        
        function nextStep() {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }
        
        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }
        
        function testConnection() {
            const formData = new FormData(document.getElementById('dbForm'));
            formData.append('action', 'test_connection');

            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> جاري اختبار الاتصال...</div>';

            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' + (data.message || 'تم الاتصال بقاعدة البيانات بنجاح!') + ' <button type="button" class="btn" onclick="nextStep()" style="margin-top: 10px;">التالي</button></div>';
                    } else {
                        resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-times-circle"></i> فشل الاتصال: ' + (data.message || 'خطأ غير معروف') + '</div>';
                    }
                } catch (parseError) {
                    console.error('Response text:', text);
                    resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-triangle"></i> خطأ في تحليل الاستجابة. تحقق من إعدادات الخادم.</div>';
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-triangle"></i> خطأ في الاتصال: ' + error.message + '</div>';
            });
        }
        
        function install() {
            // التحقق من كلمة المرور
            const password = document.querySelector('input[name="admin_password"]').value;
            const confirmPassword = document.querySelector('input[name="admin_password_confirm"]').value;

            if (password !== confirmPassword) {
                alert('كلمة المرور غير متطابقة');
                return;
            }

            if (password.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            const dbForm = new FormData(document.getElementById('dbForm'));
            const adminForm = new FormData(document.getElementById('adminForm'));

            // دمج البيانات
            const formData = new FormData();
            for (let [key, value] of dbForm) {
                formData.append(key, value);
            }
            for (let [key, value] of adminForm) {
                formData.append(key, value);
            }
            formData.append('action', 'install');

            // عرض رسالة التحميل
            const installBtn = document.querySelector('button[onclick="install()"]');
            const originalText = installBtn.innerHTML;
            installBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التثبيت...';
            installBtn.disabled = true;

            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        nextStep();
                    } else {
                        alert('خطأ في التثبيت: ' + (data.message || 'خطأ غير معروف'));
                    }
                } catch (parseError) {
                    console.error('Response text:', text);
                    alert('خطأ في تحليل استجابة الخادم. تحقق من إعدادات PHP.');
                }
            })
            .catch(error => {
                console.error('Install error:', error);
                alert('خطأ في التثبيت: ' + error.message);
            })
            .finally(() => {
                // إعادة تفعيل الزر
                installBtn.innerHTML = originalText;
                installBtn.disabled = false;
            });
        }
    </script>
</body>
</html>
