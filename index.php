<?php
/**
 * نقطة البداية الرئيسية - ExRayan Classified Ads Platform
 * Main Entry Point
 */

// تعيين ترميز الأحرف
header('Content-Type: text/html; charset=UTF-8');

// بدء عرض المحتوى
ob_start();

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Kuwait');

// تعيين مستوى الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/php_errors.log');

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// تحميل الملفات الأساسية
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/Security.php';
require_once __DIR__ . '/includes/Session.php';
require_once __DIR__ . '/includes/Validator.php';

// تحميل إعدادات التطبيق
$config = require_once __DIR__ . '/config/app.php';

// بدء الجلسة
Session::start();

// التحقق من وضع الصيانة
if ($config['maintenance']['enabled'] && !in_array(Security::getRealIpAddress(), $config['maintenance']['allowed_ips'])) {
    http_response_code(503);
    include __DIR__ . '/views/maintenance.php';
    exit;
}

// التحقق من HTTPS في الإنتاج
if ($config['env'] === 'production' && !isset($_SERVER['HTTPS'])) {
    $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header("Location: $redirectURL");
    exit;
}

// إعداد معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'ip' => Security::getRealIpAddress(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'url' => $_SERVER['REQUEST_URI'] ?? ''
    ];
    
    error_log(json_encode($errorLog, JSON_UNESCAPED_UNICODE), 3, __DIR__ . '/logs/app_errors.log');
    
    if ($config['debug']) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>خطأ:</strong> {$message} في {$file} السطر {$line}";
        echo "</div>";
    }
    
    return true;
});

// إعداد معالج الاستثناءات
set_exception_handler(function($exception) use ($config) {
    $errorLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => 'Exception',
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'ip' => Security::getRealIpAddress(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'url' => $_SERVER['REQUEST_URI'] ?? ''
    ];
    
    error_log(json_encode($errorLog, JSON_UNESCAPED_UNICODE), 3, __DIR__ . '/logs/app_errors.log');
    
    if ($config['debug']) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>استثناء:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>الملف:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>السطر:</strong> " . $exception->getLine();
        echo "</div>";
    } else {
        http_response_code(500);
        include __DIR__ . '/views/errors/500.php';
    }
});

// الحصول على المسار المطلوب
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];
$requestUri = str_replace(dirname($scriptName), '', $requestUri);
$requestUri = trim($requestUri, '/');

// تحليل المسار
$segments = explode('/', $requestUri);
$controller = $segments[0] ?: 'home';
$action = $segments[1] ?? 'index';
$params = array_slice($segments, 2);

// تنظيف المدخلات
$controller = Security::sanitizeInput($controller);
$action = Security::sanitizeInput($action);
$params = array_map([Security::class, 'sanitizeInput'], $params);

// التحقق من صحة اسم الكنترولر
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $controller)) {
    $controller = 'home';
}

if (!preg_match('/^[a-zA-Z0-9_-]+$/', $action)) {
    $action = 'index';
}

// تحديد مسار الكنترولر
$controllerFile = __DIR__ . "/controllers/{$controller}.php";

try {
    // التحقق من وجود الكنترولر
    if (!file_exists($controllerFile)) {
        throw new Exception("الصفحة غير موجودة", 404);
    }
    
    // تحميل الكنترولر
    require_once $controllerFile;
    
    // تحديد اسم فئة الكنترولر
    $controllerClass = ucfirst($controller) . 'Controller';
    
    // التحقق من وجود الفئة
    if (!class_exists($controllerClass)) {
        throw new Exception("كنترولر غير صحيح", 404);
    }
    
    // إنشاء مثيل من الكنترولر
    $controllerInstance = new $controllerClass();
    
    // التحقق من وجود الطريقة
    if (!method_exists($controllerInstance, $action)) {
        throw new Exception("الإجراء غير موجود", 404);
    }
    
    // تنفيذ الطريقة
    call_user_func_array([$controllerInstance, $action], $params);
    
} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);
    
    // تسجيل الخطأ
    error_log("Error {$errorCode}: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());
    
    // عرض صفحة الخطأ المناسبة
    $errorPage = __DIR__ . "/views/errors/{$errorCode}.php";
    if (file_exists($errorPage)) {
        include $errorPage;
    } else {
        include __DIR__ . '/views/errors/500.php';
    }
}

// إنهاء عرض المحتوى
ob_end_flush();
?>
