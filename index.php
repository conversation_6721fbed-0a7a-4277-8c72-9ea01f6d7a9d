<?php
/**
 * نقطة الدخول المبسطة - ExRayan Platform
 * Simple Entry Point
 */

// تعيين ترميز الأحرف
header('Content-Type: text/html; charset=UTF-8');

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Kuwait');

// تعيين مستوى الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// إنشاء مجلد السجلات إذا لم يكن موجوداً
if (!is_dir(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

ini_set('error_log', __DIR__ . '/logs/app_errors.log');

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

try {
    // التحقق من وجود ملف التثبيت
    if (!file_exists(__DIR__ . '/.installed')) {
        // إذا لم يكن النظام مثبت، عرض صفحة الترحيب
        $requestUri = $_SERVER['REQUEST_URI'];
        $requestUri = parse_url($requestUri, PHP_URL_PATH);
        $requestUri = trim($requestUri, '/');

        // السماح بالوصول لملفات التثبيت والاختبار
        $allowedFiles = ['install.php', 'test_connection.php', 'create_admin.php', 'welcome.php'];
        $currentFile = basename($_SERVER['SCRIPT_NAME']);

        if (empty($requestUri) && !in_array($currentFile, $allowedFiles)) {
            include __DIR__ . '/welcome.php';
            exit;
        }
    }

    // تحميل الملفات الأساسية
    require_once __DIR__ . '/includes/Database.php';
    require_once __DIR__ . '/includes/Security.php';
    require_once __DIR__ . '/includes/Session.php';
    require_once __DIR__ . '/includes/Validator.php';

    // اختبار الاتصال بقاعدة البيانات
    try {
        $db = Database::getInstance();
        $db->selectOne("SELECT 1");
    } catch (Exception $dbError) {
        // إذا فشل الاتصال بقاعدة البيانات، عرض صفحة الترحيب
        include __DIR__ . '/welcome.php';
        exit;
    }

    // بدء الجلسة
    Session::start();

    // التحقق من وضع الصيانة
    if (($_ENV['APP_MAINTENANCE'] ?? 'false') === 'true') {
        $user = Session::getUser();
        if (!$user || $user['classification'] !== 'admin') {
            include __DIR__ . '/views/maintenance.php';
            exit;
        }
    }

    // الحصول على المسار المطلوب
    $requestUri = $_GET['route'] ?? '';
    $requestUri = trim($requestUri, '/');

    // إذا كان المسار فارغ، عرض الصفحة الرئيسية
    if (empty($requestUri) || $requestUri === '/') {
        // التحقق من وجود ملف الصفحة الرئيسية
        if (file_exists(__DIR__ . '/views/home/<USER>')) {
            include __DIR__ . '/views/home/<USER>';
        } else {
            // عرض صفحة بسيطة إذا لم توجد
            echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>ExRayan - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="text-center">
            <h1 class="text-primary">مرحباً بك في ExRayan</h1>
            <p class="lead">منصة الإعلانات المبوبة</p>
            <a href="admin/" class="btn btn-primary">لوحة التحكم</a>
            <a href="welcome.php" class="btn btn-secondary">صفحة الترحيب</a>
        </div>
    </div>
</body>
</html>';
        }
        exit;
    }

    // تحليل المسار
    $segments = explode('/', $requestUri);
    $controller = $segments[0] ?: 'home';
    $action = $segments[1] ?? 'index';
    $params = array_slice($segments, 2);

    // تنظيف المدخلات
    $controller = Security::sanitizeInput($controller);
    $action = Security::sanitizeInput($action);
    $params = array_map([Security::class, 'sanitizeInput'], $params);

    // التحقق من صحة اسم الكنترولر
    if (!preg_match('/^[a-zA-Z0-9_-]+$/', $controller)) {
        $controller = 'home';
    }

    if (!preg_match('/^[a-zA-Z0-9_-]+$/', $action)) {
        $action = 'index';
    }

    // تحديد مسار الكنترولر
    $controllerFile = __DIR__ . "/controllers/{$controller}.php";

    // التحقق من وجود الكنترولر
    if (!file_exists($controllerFile)) {
        throw new Exception("الصفحة غير موجودة", 404);
    }

    // تحميل الكنترولر
    require_once $controllerFile;

    // تحديد اسم فئة الكنترولر
    $controllerClass = ucfirst($controller) . 'Controller';

    // التحقق من وجود الفئة
    if (!class_exists($controllerClass)) {
        throw new Exception("كنترولر غير صحيح", 404);
    }

    // إنشاء مثيل من الكنترولر
    $controllerInstance = new $controllerClass();

    // التحقق من وجود الطريقة
    if (!method_exists($controllerInstance, $action)) {
        throw new Exception("الإجراء غير موجود", 404);
    }

    // تنفيذ الطريقة
    call_user_func_array([$controllerInstance, $action], $params);

} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);

    // تسجيل الخطأ
    error_log("Application Error {$errorCode}: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

    // عرض صفحة الخطأ المناسبة
    $errorPage = __DIR__ . "/views/errors/{$errorCode}.php";
    if (file_exists($errorPage)) {
        include $errorPage;
    } else {
        include __DIR__ . '/views/errors/500.php';
    }
}
?>
