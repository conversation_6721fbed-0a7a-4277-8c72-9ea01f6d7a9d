<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - لوحة التحكم | ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .error-icon {
            font-size: 5rem;
            color: #667eea;
            margin-bottom: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-block;
            margin: 0 10px;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .quick-links {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .quick-links h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .quick-link {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            margin: 5px;
            font-size: 0.9rem;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        
        .quick-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .floating-icons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .icon-1 { top: 20%; left: 10%; animation-delay: 0s; }
        .icon-2 { top: 60%; right: 15%; animation-delay: 2s; }
        .icon-3 { bottom: 20%; left: 20%; animation-delay: 4s; }
        .icon-4 { top: 30%; right: 30%; animation-delay: 1s; }
    </style>
</head>
<body>
    <div class="floating-icons">
        <div class="floating-icon icon-1">
            <i class="fas fa-tachometer-alt fa-3x"></i>
        </div>
        <div class="floating-icon icon-2">
            <i class="fas fa-users fa-3x"></i>
        </div>
        <div class="floating-icon icon-3">
            <i class="fas fa-bullhorn fa-3x"></i>
        </div>
        <div class="floating-icon icon-4">
            <i class="fas fa-cog fa-3x"></i>
        </div>
    </div>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.<br>
            يمكنك العودة للوحة التحكم أو استخدام الروابط السريعة أدناه.
        </p>
        
        <div class="mt-4">
            <a href="index.php" class="btn-home">
                <i class="fas fa-tachometer-alt me-2"></i>
                العودة للوحة التحكم
            </a>
            
            <a href="../" class="btn-home btn-secondary">
                <i class="fas fa-home me-2"></i>
                الموقع الرئيسي
            </a>
        </div>
        
        <div class="quick-links">
            <h6>روابط سريعة:</h6>
            
            <a href="ads" class="quick-link">
                <i class="fas fa-bullhorn me-1"></i>
                إدارة الإعلانات
            </a>
            
            <a href="users" class="quick-link">
                <i class="fas fa-users me-1"></i>
                إدارة المستخدمين
            </a>
            
            <a href="sections" class="quick-link">
                <i class="fas fa-folder me-1"></i>
                إدارة الأقسام
            </a>
            
            <a href="stores" class="quick-link">
                <i class="fas fa-store me-1"></i>
                إدارة المتاجر
            </a>
            
            <a href="reports" class="quick-link">
                <i class="fas fa-chart-bar me-1"></i>
                التقارير
            </a>
            
            <a href="settings" class="quick-link">
                <i class="fas fa-cog me-1"></i>
                الإعدادات
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المطور
            </small>
        </div>
    </div>
    
    <script>
        // البحث التلقائي في الروابط
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const pathSegments = currentPath.split('/').filter(segment => segment);
            
            // اقتراح روابط بناءً على المسار
            if (pathSegments.length > 0) {
                const lastSegment = pathSegments[pathSegments.length - 1];
                const suggestions = document.querySelectorAll('.quick-link');
                
                suggestions.forEach(link => {
                    if (link.href.includes(lastSegment)) {
                        link.style.background = '#fff3cd';
                        link.style.borderColor = '#ffc107';
                    }
                });
            }
        });
    </script>
</body>
</html>
