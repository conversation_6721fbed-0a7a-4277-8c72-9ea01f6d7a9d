<?php
/**
 * إصلاح صلاحيات المدير - ExRayan Platform
 * Fix Admin User Permissions
 */

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=UTF-8');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'fix_admin') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $userId = $_POST['user_id'] ?? '';
            
            if (empty($userId)) {
                throw new Exception('معرف المستخدم مطلوب');
            }
            
            // تحديث تصنيف المستخدم إلى admin
            $stmt = $pdo->prepare("UPDATE users SET classification = 'admin', role_id = 1, is_active = 1 WHERE id = ?");
            $result = $stmt->execute([$userId]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تحديث صلاحيات المستخدم بنجاح'
                ]);
            } else {
                throw new Exception('لم يتم العثور على المستخدم أو لم يتم التحديث');
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    if ($action === 'create_admin') {
        try {
            // تحميل متغيرات البيئة
            if (file_exists('.env')) {
                $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos($line, '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value, '"\'');
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
            $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
            $username = $_ENV['DB_USERNAME'] ?? 'root';
            $password = $_ENV['DB_PASSWORD'] ?? '000';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $adminUsername = $_POST['admin_username'] ?? 'admin';
            $adminEmail = $_POST['admin_email'] ?? '<EMAIL>';
            $adminPassword = $_POST['admin_password'] ?? 'admin123';
            
            // تشفير كلمة المرور
            $hashedPassword = password_hash($adminPassword, PASSWORD_ARGON2ID);
            
            // التحقق من وجود مدير
            $stmt = $pdo->prepare("SELECT id FROM users WHERE classification = 'admin' LIMIT 1");
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                // تحديث المدير الموجود
                $adminId = $stmt->fetchColumn();
                $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ?, is_active = 1, email_verified_at = NOW() WHERE id = ?");
                $stmt->execute([$adminUsername, $adminEmail, $hashedPassword, $adminId]);
                $message = 'تم تحديث حساب المدير الموجود';
            } else {
                // إنشاء مدير جديد
                $stmt = $pdo->prepare("INSERT INTO users (username, email, password, role_id, classification, is_active, email_verified_at, created_at) VALUES (?, ?, ?, 1, 'admin', 1, NOW(), NOW())");
                $stmt->execute([$adminUsername, $adminEmail, $hashedPassword]);
                $message = 'تم إنشاء حساب مدير جديد';
            }
            
            echo json_encode([
                'success' => true,
                'message' => $message
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}

// جلب بيانات المستخدمين
$users = [];
$adminExists = false;

try {
    // تحميل متغيرات البيئة
    if (file_exists('.env')) {
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value, '"\'');
            }
        }
    }
    
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '000';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب جميع المستخدمين
    $stmt = $pdo->query("SELECT id, username, email, classification, role_id, is_active, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // التحقق من وجود مدير
    foreach ($users as $user) {
        if ($user['classification'] === 'admin') {
            $adminExists = true;
            break;
        }
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح صلاحيات المدير - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .fix-container { background: white; border-radius: 15px; padding: 30px; max-width: 1000px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-fix { background: #28a745; border: none; color: white; padding: 8px 15px; border-radius: 5px; margin: 2px; }
        .btn-fix:hover { background: #218838; }
        .user-table { font-size: 0.9rem; }
        .badge { font-size: 0.75rem; }
        .alert { border-radius: 8px; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-user-shield text-primary"></i> إصلاح صلاحيات المدير</h1>
            <p class="text-muted">أداة لإصلاح مشاكل صلاحيات الوصول للوحة التحكم</p>
        </div>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            خطأ في الاتصال بقاعدة البيانات: <?= htmlspecialchars($error) ?>
        </div>
        <?php else: ?>
        
        <!-- حالة المدير -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-user-shield me-2"></i>
                            حالة المدير
                        </h5>
                        <?php if ($adminExists): ?>
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <p>يوجد حساب مدير في النظام</p>
                            </div>
                        <?php else: ?>
                            <div class="text-danger">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <p>لا يوجد حساب مدير في النظام</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء/تحديث مدير
                        </h5>
                        <form id="createAdminForm">
                            <div class="mb-2">
                                <input type="text" name="admin_username" class="form-control form-control-sm" placeholder="اسم المستخدم" value="admin">
                            </div>
                            <div class="mb-2">
                                <input type="email" name="admin_email" class="form-control form-control-sm" placeholder="البريد الإلكتروني" value="<EMAIL>">
                            </div>
                            <div class="mb-2">
                                <input type="password" name="admin_password" class="form-control form-control-sm" placeholder="كلمة المرور" value="admin123">
                            </div>
                            <button type="button" class="btn btn-fix w-100" onclick="createAdmin()">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء/تحديث مدير
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قائمة المستخدمين -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين المسجلين (<?= count($users) ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا يوجد مستخدمين في النظام</p>
                    </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover user-table">
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>التصنيف</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?= $user['id'] ?></td>
                                <td><?= htmlspecialchars($user['username']) ?></td>
                                <td><?= htmlspecialchars($user['email']) ?></td>
                                <td>
                                    <?php
                                    $badgeClass = $user['classification'] === 'admin' ? 'bg-danger' : 
                                                 ($user['classification'] === 'moderator' ? 'bg-warning' : 'bg-secondary');
                                    ?>
                                    <span class="badge <?= $badgeClass ?>"><?= $user['classification'] ?></span>
                                </td>
                                <td>
                                    <span class="badge <?= $user['is_active'] ? 'bg-success' : 'bg-secondary' ?>">
                                        <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                    </span>
                                </td>
                                <td><?= date('Y-m-d H:i', strtotime($user['created_at'])) ?></td>
                                <td>
                                    <?php if ($user['classification'] !== 'admin'): ?>
                                    <button class="btn btn-fix btn-sm" onclick="fixUser(<?= $user['id'] ?>)">
                                        <i class="fas fa-user-shield"></i>
                                        جعله مدير
                                    </button>
                                    <?php else: ?>
                                    <span class="text-success">
                                        <i class="fas fa-check"></i>
                                        مدير
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php endif; ?>
        
        <div id="result" class="mt-3"></div>
        
        <div class="text-center mt-4">
            <a href="welcome.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <a href="admin/" class="btn btn-primary">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </a>
        </div>
    </div>
    
    <script>
        function fixUser(userId) {
            if (!confirm('هل تريد جعل هذا المستخدم مديراً؟')) {
                return;
            }
            
            executeAction('fix_admin', {user_id: userId}, 'تحديث صلاحيات المستخدم');
        }
        
        function createAdmin() {
            const formData = new FormData(document.getElementById('createAdminForm'));
            const data = Object.fromEntries(formData);
            
            executeAction('create_admin', data, 'إنشاء/تحديث المدير');
        }
        
        function executeAction(action, data, actionName) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري ${actionName}...</div>`;
            
            const formData = new FormData();
            formData.append('action', action);
            
            for (const [key, value] of Object.entries(data)) {
                formData.append(key, value);
            }
            
            fetch('fix_admin_user.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const result = JSON.parse(text);
                    if (result.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                ${result.message}
                                <br><br>
                                <button class="btn btn-primary" onclick="location.reload()">تحديث الصفحة</button>
                                <a href="admin/" class="btn btn-success">الذهاب للوحة التحكم</a>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>خطأ: ${result.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ في تحليل الاستجابة</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
