<?php
/**
 * فحص حالة التثبيت - ExRayan Platform
 * Installation Status Checker
 */

header('Content-Type: application/json; charset=UTF-8');

$status = [
    'installed' => false,
    'database_connected' => false,
    'tables_exist' => false,
    'admin_exists' => false,
    'errors' => [],
    'recommendations' => []
];

try {
    // فحص ملف التثبيت
    $status['installed'] = file_exists('.installed');
    
    // تحميل متغيرات البيئة
    if (file_exists('.env')) {
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value, '"\'');
            }
        }
    }
    
    // فحص الاتصال بقاعدة البيانات
    try {
        $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
        $dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '000';
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $status['database_connected'] = true;
        
        // فحص وجود الجداول الأساسية
        $required_tables = ['users', 'sections', 'ads', 'settings'];
        $stmt = $pdo->query("SHOW TABLES");
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        
        if (empty($missing_tables)) {
            $status['tables_exist'] = true;
            
            // فحص وجود مدير
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE classification = 'admin'");
            $stmt->execute();
            $result = $stmt->fetch();
            
            $status['admin_exists'] = $result['count'] > 0;
            $status['admin_count'] = $result['count'];
            
            // معلومات إضافية
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            $status['total_users'] = $result['count'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM ads");
            $result = $stmt->fetch();
            $status['total_ads'] = $result['count'];
            
        } else {
            $status['missing_tables'] = $missing_tables;
        }
        
        $status['existing_tables'] = $existing_tables;
        $status['tables_count'] = count($existing_tables);
        
    } catch (PDOException $e) {
        $status['database_error'] = $e->getMessage();
        $status['errors'][] = 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage();
    }
    
    // تحديد التوصيات
    if (!$status['database_connected']) {
        $status['recommendations'][] = 'تحقق من إعدادات قاعدة البيانات في ملف .env';
        $status['recommendations'][] = 'تأكد من تشغيل خادم MySQL';
        $status['recommendations'][] = 'استخدم test_connection.php لاختبار الاتصال';
    } elseif (!$status['tables_exist']) {
        $status['recommendations'][] = 'قم بتشغيل ملف التثبيت install.php';
        $status['recommendations'][] = 'أو استخدم reset_database.php لإعادة التعيين';
    } elseif (!$status['admin_exists']) {
        $status['recommendations'][] = 'قم بإنشاء حساب مدير من create_admin.php';
        $status['recommendations'][] = 'أو أعد تشغيل التثبيت';
    } else {
        $status['recommendations'][] = 'النظام جاهز للاستخدام!';
        $status['recommendations'][] = 'يمكنك الوصول للوحة التحكم من admin/';
    }
    
    // تحديد الحالة العامة
    if ($status['database_connected'] && $status['tables_exist'] && $status['admin_exists']) {
        $status['overall_status'] = 'ready';
        $status['message'] = 'النظام مثبت ومُعد بالكامل';
    } elseif ($status['database_connected'] && $status['tables_exist']) {
        $status['overall_status'] = 'needs_admin';
        $status['message'] = 'النظام مثبت لكن يحتاج لحساب مدير';
    } elseif ($status['database_connected']) {
        $status['overall_status'] = 'needs_installation';
        $status['message'] = 'قاعدة البيانات متصلة لكن تحتاج للتثبيت';
    } else {
        $status['overall_status'] = 'needs_database';
        $status['message'] = 'يحتاج لإعداد قاعدة البيانات';
    }
    
} catch (Exception $e) {
    $status['errors'][] = 'خطأ عام: ' . $e->getMessage();
    $status['overall_status'] = 'error';
    $status['message'] = 'حدث خطأ أثناء فحص التثبيت';
}

// إضافة معلومات إضافية
$status['timestamp'] = date('Y-m-d H:i:s');
$status['php_version'] = PHP_VERSION;
$status['server'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';

echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
