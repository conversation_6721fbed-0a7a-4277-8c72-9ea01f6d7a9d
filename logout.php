<?php
/**
 * تسجيل الخروج - ExRayan Platform
 * Logout Script
 */

// تحميل الملفات الأساسية
require_once __DIR__ . '/includes/Session.php';
require_once __DIR__ . '/includes/Security.php';

// بدء الجلسة
Session::start();

// التحقق من تسجيل الدخول
if (Session::isLoggedIn()) {
    $user = Session::getUser();
    
    // تسجيل نشاط تسجيل الخروج
    if ($user) {
        try {
            require_once __DIR__ . '/includes/Database.php';
            $db = Database::getInstance();
            
            $db->insert(
                "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                 VALUES (?, 'logout', ?, ?, ?, NOW())",
                [
                    $user['id'],
                    json_encode(['logout_time' => date('Y-m-d H:i:s')]),
                    Security::getRealIpAddress(),
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]
            );
        } catch (Exception $e) {
            // تسجيل الخطأ دون إيقاف عملية تسجيل الخروج
            error_log("Logout logging error: " . $e->getMessage());
        }
    }
    
    // تسجيل الخروج
    Session::logout();
    
    // رسالة نجاح
    Session::flash('success', 'تم تسجيل الخروج بنجاح');
}

// إعادة التوجيه
$redirect = $_GET['redirect'] ?? '/';
header('Location: ' . $redirect);
exit;
?>
