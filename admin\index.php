<?php
/**
 * لوحة التحكم الرئيسية - ExRayan Platform
 * Admin Dashboard Main Entry Point
 */

// تعيين ترميز الأحرف
header('Content-Type: text/html; charset=UTF-8');

// بدء عرض المحتوى
ob_start();

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Kuwait');

// تعيين مستوى الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/admin_errors.log');

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// تحميل الملفات الأساسية
require_once __DIR__ . '/../includes/Database.php';
require_once __DIR__ . '/../includes/Security.php';
require_once __DIR__ . '/../includes/Session.php';
require_once __DIR__ . '/../includes/Validator.php';

// بدء الجلسة
Session::start();

// التحقق من تسجيل الدخول
if (!Session::isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات الإدارة
$user = Session::getUser();
if (!$user) {
    Session::flash('error', 'يجب تسجيل الدخول أولاً');
    header('Location: login.php');
    exit;
}

// التحقق من صلاحيات الإدارة مع تسجيل مفصل
$userClassification = $user['classification'] ?? '';
$allowedRoles = ['admin', 'moderator'];

if (!in_array($userClassification, $allowedRoles)) {
    // تسجيل محاولة الوصول غير المصرح بها
    error_log("Unauthorized admin access attempt - User ID: " . ($user['id'] ?? 'unknown') . ", Classification: " . $userClassification);

    Session::flash('error', "ليس لديك صلاحية للوصول لهذه الصفحة. تصنيفك الحالي: " . $userClassification);
    header('Location: ../');
    exit;
}

// الحصول على المسار المطلوب
$requestUri = $_SERVER['REQUEST_URI'];
$adminPath = '/admin/';
$requestUri = str_replace($adminPath, '', $requestUri);
$requestUri = trim($requestUri, '/');

// تحليل المسار
$segments = explode('/', $requestUri);
$controller = $segments[0] ?: 'dashboard';
$action = $segments[1] ?? 'index';
$params = array_slice($segments, 2);

// تنظيف المدخلات
$controller = Security::sanitizeInput($controller);
$action = Security::sanitizeInput($action);
$params = array_map([Security::class, 'sanitizeInput'], $params);

// التحقق من صحة اسم الكنترولر
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $controller)) {
    $controller = 'dashboard';
}

if (!preg_match('/^[a-zA-Z0-9_-]+$/', $action)) {
    $action = 'index';
}

// تحديد مسار الكنترولر
$controllerFile = __DIR__ . "/controllers/{$controller}.php";

try {
    // التحقق من وجود الكنترولر
    if (!file_exists($controllerFile)) {
        throw new Exception("الصفحة غير موجودة", 404);
    }
    
    // تحميل الكنترولر
    require_once $controllerFile;
    
    // تحديد اسم فئة الكنترولر
    $controllerClass = ucfirst($controller) . 'Controller';
    
    // التحقق من وجود الفئة
    if (!class_exists($controllerClass)) {
        throw new Exception("كنترولر غير صحيح", 404);
    }
    
    // إنشاء مثيل من الكنترولر
    $controllerInstance = new $controllerClass();
    
    // التحقق من وجود الطريقة
    if (!method_exists($controllerInstance, $action)) {
        throw new Exception("الإجراء غير موجود", 404);
    }
    
    // تنفيذ الطريقة
    call_user_func_array([$controllerInstance, $action], $params);
    
} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);
    
    // تسجيل الخطأ
    error_log("Admin Error {$errorCode}: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());
    
    // عرض صفحة الخطأ المناسبة
    $errorPage = __DIR__ . "/views/errors/{$errorCode}.php";
    if (file_exists($errorPage)) {
        include $errorPage;
    } else {
        include __DIR__ . '/views/errors/500.php';
    }
}

// إنهاء عرض المحتوى
ob_end_flush();
?>
