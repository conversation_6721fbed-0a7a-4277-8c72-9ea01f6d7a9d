# ExRayan Classified Ads Platform - Apache Configuration
# إعدادات Apache للمشروع

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(htaccess|htpasswd|ini|phps|fla|psd|log|sh)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول للمجلدات الحساسة
RedirectMatch 404 /\.git
RedirectMatch 404 /config/
RedirectMatch 404 /includes/
RedirectMatch 404 /logs/
RedirectMatch 404 /backups/

# إعدادات الأمان
ServerSignature Off
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

# إعدادات HTTPS (في حالة الإنتاج)
# Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# دعم UTF-8
AddDefaultCharset UTF-8

# إعدادات MIME
AddType application/javascript .js
AddType text/css .css

# إعادة توجيه الروابط الودية
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# إعادة توجيه www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# إعادة توجيه HTTP إلى HTTPS (في الإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# منع الوصول المباشر للملفات PHP في مجلدات معينة
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# منع الوصول للملفات المخفية
<IfModule mod_rewrite.c>
    RewriteCond %{SCRIPT_FILENAME} -d [OR]
    RewriteCond %{SCRIPT_FILENAME} -f
    RewriteRule "(^|/)\." - [F]
</IfModule>

# إعدادات خاصة بالصور
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg)$">
    Header set Cache-Control "public, max-age=2592000"
</FilesMatch>

# إعدادات خاصة بملفات CSS و JavaScript
<FilesMatch "\.(css|js)$">
    Header set Cache-Control "public, max-age=604800"
</FilesMatch>

# منع عرض قائمة الملفات
Options -Indexes

# صفحات الأخطاء المخصصة
ErrorDocument 400 /views/errors/400.php
ErrorDocument 401 /views/errors/401.php
ErrorDocument 403 /views/errors/403.php
ErrorDocument 404 /views/errors/404.php
ErrorDocument 500 /views/errors/500.php
ErrorDocument 503 /views/errors/503.php
