<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات - ExRayan Platform
 * Database Connection Test
 */

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// معلومات قاعدة البيانات
$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'exrayan';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '000';
$port = $_ENV['DB_PORT'] ?? '3306';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-title {
            color: #667eea;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .test-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #667eea;
            font-family: monospace;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .btn-test {
            background: #667eea;
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-test:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .result-box {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="test-title">
                    <i class="fas fa-database me-2"></i>
                    اختبار الاتصال بقاعدة البيانات
                </h1>
                <p class="test-subtitle">ExRayan Platform - Database Connection Test</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الاتصال
                    </h5>
                    
                    <div class="info-item">
                        <span class="info-label">الخادم:</span>
                        <span class="info-value"><?= htmlspecialchars($host) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">المنفذ:</span>
                        <span class="info-value"><?= htmlspecialchars($port) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">قاعدة البيانات:</span>
                        <span class="info-value"><?= htmlspecialchars($dbname) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">اسم المستخدم:</span>
                        <span class="info-value"><?= htmlspecialchars($username) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">كلمة المرور:</span>
                        <span class="info-value"><?= str_repeat('*', strlen($password)) ?></span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="fas fa-cogs me-2"></i>
                        فحص المتطلبات
                    </h5>
                    
                    <?php
                    $requirements = [
                        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                        'PDO Extension' => extension_loaded('pdo'),
                        'PDO MySQL' => extension_loaded('pdo_mysql'),
                        'MySQLi Extension' => extension_loaded('mysqli'),
                        'JSON Extension' => extension_loaded('json'),
                        'OpenSSL Extension' => extension_loaded('openssl'),
                    ];
                    
                    foreach ($requirements as $requirement => $status) {
                        $statusClass = $status ? 'status-success' : 'status-error';
                        $statusIcon = $status ? 'fas fa-check' : 'fas fa-times';
                        $statusText = $status ? 'متوفر' : 'غير متوفر';
                        
                        echo "<div class='info-item'>";
                        echo "<span class='info-label'>{$requirement}:</span>";
                        echo "<span class='{$statusClass}'><i class='{$statusIcon} me-1'></i>{$statusText}</span>";
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button class="btn btn-test" onclick="testConnection()">
                    <i class="fas fa-play me-2"></i>
                    اختبار الاتصال
                </button>
            </div>
            
            <div id="result" class="result-box"></div>
        </div>
        
        <?php if (isset($_POST['test'])): ?>
        <div class="test-card">
            <h5 class="mb-3">
                <i class="fas fa-flask me-2"></i>
                نتيجة الاختبار
            </h5>
            
            <?php
            try {
                // اختبار الاتصال
                $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);
                
                echo "<div class='alert alert-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>نجح الاتصال!</strong> تم الاتصال بقاعدة البيانات بنجاح.";
                echo "</div>";
                
                // اختبار الجداول
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (count($tables) > 0) {
                    echo "<div class='alert alert-info'>";
                    echo "<i class='fas fa-table me-2'></i>";
                    echo "<strong>الجداول الموجودة:</strong> " . count($tables) . " جدول";
                    echo "<br><small>الجداول: " . implode(', ', array_slice($tables, 0, 10));
                    if (count($tables) > 10) echo " و " . (count($tables) - 10) . " جدول آخر";
                    echo "</small></div>";
                } else {
                    echo "<div class='alert alert-warning'>";
                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                    echo "<strong>تحذير:</strong> قاعدة البيانات فارغة. يرجى استيراد ملف database_schema.sql";
                    echo "</div>";
                }
                
                // اختبار إعدادات MySQL
                $stmt = $pdo->query("SELECT @@version as version, @@character_set_database as charset");
                $info = $stmt->fetch();
                
                echo "<div class='info-item'>";
                echo "<span class='info-label'>إصدار MySQL:</span>";
                echo "<span class='info-value'>{$info['version']}</span>";
                echo "</div>";
                
                echo "<div class='info-item'>";
                echo "<span class='info-label'>ترميز قاعدة البيانات:</span>";
                echo "<span class='info-value'>{$info['charset']}</span>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>فشل الاتصال!</strong><br>";
                echo "الخطأ: " . htmlspecialchars($e->getMessage());
                echo "</div>";
                
                echo "<div class='alert alert-info'>";
                echo "<h6>خطوات حل المشكلة:</h6>";
                echo "<ol>";
                echo "<li>تأكد من تشغيل خادم MySQL</li>";
                echo "<li>تحقق من صحة معلومات الاتصال في ملف .env</li>";
                echo "<li>تأكد من وجود قاعدة البيانات 'exrayan'</li>";
                echo "<li>تحقق من صلاحيات المستخدم</li>";
                echo "</ol>";
                echo "</div>";
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="text-center">
            <a href="/" class="btn btn-outline-light">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <a href="install.php" class="btn btn-outline-light ms-2">
                <i class="fas fa-download me-2"></i>
                ملف التثبيت
            </a>
        </div>
    </div>
    
    <script>
        function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-box';
            resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الاتصال...';
            
            // إرسال طلب POST لاختبار الاتصال
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';
            
            const input = document.createElement('input');
            input.name = 'test';
            input.value = '1';
            form.appendChild(input);
            
            document.body.appendChild(form);
            form.submit();
        }
    </script>
</body>
</html>
