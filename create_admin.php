<?php
/**
 * إنشاء حساب مدير سريع - ExRayan Platform
 * Quick Admin Account Creator
 */

// منع الوصول إذا كان المشروع مثبت مسبقاً
if (file_exists('.installed')) {
    die('المشروع مثبت مسبقاً. استخدم ملف install.php للتثبيت الكامل.');
}

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// تحميل الملفات الأساسية
require_once __DIR__ . '/includes/Database.php';
require_once __DIR__ . '/includes/Security.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = Security::sanitizeInput($_POST['username'] ?? '');
        $email = Security::sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // التحقق من البيانات
        if (empty($username) || empty($email) || empty($password)) {
            throw new Exception('جميع الحقول مطلوبة');
        }
        
        if (!Security::validateEmail($email)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        if ($password !== $confirmPassword) {
            throw new Exception('كلمة المرور غير متطابقة');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }
        
        // الاتصال بقاعدة البيانات
        $db = Database::getInstance();
        
        // التحقق من عدم وجود المستخدم مسبقاً
        $existingUser = $db->selectOne(
            "SELECT id FROM users WHERE username = ? OR email = ?", 
            [$username, $email]
        );
        
        if ($existingUser) {
            throw new Exception('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً');
        }
        
        // تشفير كلمة المرور
        $hashedPassword = Security::hashPassword($password);
        
        // إنشاء الحساب
        $userId = $db->insert(
            "INSERT INTO users (username, email, password, role_id, classification, is_active, email_verified_at, created_at) 
             VALUES (?, ?, ?, 1, 'admin', 1, NOW(), NOW())",
            [$username, $email, $hashedPassword]
        );
        
        if ($userId) {
            $message = "تم إنشاء حساب المدير بنجاح! يمكنك الآن تسجيل الدخول.";
            $messageType = 'success';
        } else {
            throw new Exception('فشل في إنشاء الحساب');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب مدير - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        
        .admin-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .admin-title {
            color: #667eea;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .admin-subtitle {
            color: #6c757d;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-create {
            background: #667eea;
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s;
        }
        
        .btn-create:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="admin-card">
        <div class="admin-header">
            <h1 class="admin-title">
                <i class="fas fa-user-shield me-2"></i>
                إنشاء حساب مدير
            </h1>
            <p class="admin-subtitle">ExRayan Platform - Admin Account Creator</p>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType === 'success' ? 'success' : 'danger' ?>">
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($messageType !== 'success'): ?>
        <form method="POST">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-user me-1"></i>
                    اسم المستخدم
                </label>
                <input type="text" name="username" class="form-control" value="<?= htmlspecialchars($_POST['username'] ?? 'admin') ?>" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-envelope me-1"></i>
                    البريد الإلكتروني
                </label>
                <input type="email" name="email" class="form-control" value="<?= htmlspecialchars($_POST['email'] ?? '<EMAIL>') ?>" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-lock me-1"></i>
                    كلمة المرور
                </label>
                <input type="password" name="password" class="form-control" required>
                <small class="text-muted">يجب أن تكون 6 أحرف على الأقل</small>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-lock me-1"></i>
                    تأكيد كلمة المرور
                </label>
                <input type="password" name="confirm_password" class="form-control" required>
            </div>
            
            <button type="submit" class="btn btn-create">
                <i class="fas fa-plus me-2"></i>
                إنشاء حساب المدير
            </button>
        </form>
        <?php endif; ?>
        
        <div class="links">
            <a href="test_connection.php">
                <i class="fas fa-database me-1"></i>
                اختبار قاعدة البيانات
            </a>
            
            <a href="install.php">
                <i class="fas fa-download me-1"></i>
                التثبيت الكامل
            </a>
            
            <?php if ($messageType === 'success'): ?>
            <br><br>
            <a href="/" class="btn btn-outline-primary">
                <i class="fas fa-home me-1"></i>
                الذهاب للموقع
            </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
