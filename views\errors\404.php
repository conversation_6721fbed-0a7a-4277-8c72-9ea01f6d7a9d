<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - ExRayan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 40px;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-home {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-block;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: #667eea;
            text-decoration: none;
        }
        
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .icon-1 { top: 20%; left: 10%; animation-delay: 0s; }
        .icon-2 { top: 60%; right: 15%; animation-delay: 2s; }
        .icon-3 { bottom: 20%; left: 20%; animation-delay: 4s; }
    </style>
</head>
<body>
    <div class="floating-icon icon-1">
        <i class="fas fa-search fa-3x"></i>
    </div>
    <div class="floating-icon icon-2">
        <i class="fas fa-store fa-3x"></i>
    </div>
    <div class="floating-icon icon-3">
        <i class="fas fa-home fa-3x"></i>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
        </p>
        
        <a href="/" class="btn-home">
            <i class="fas fa-home me-2"></i>
            العودة للصفحة الرئيسية
        </a>
        
        <div class="mt-4">
            <p class="mb-2">أو يمكنك:</p>
            <a href="/search" class="text-white me-3">
                <i class="fas fa-search me-1"></i>
                البحث في الموقع
            </a>
            <a href="/contact" class="text-white">
                <i class="fas fa-envelope me-1"></i>
                التواصل معنا
            </a>
        </div>
    </div>
</body>
</html>
