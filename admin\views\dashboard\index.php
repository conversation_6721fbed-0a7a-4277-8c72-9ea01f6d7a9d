<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'لوحة التحكم') ?> | <PERSON><PERSON><PERSON><PERSON>min</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --sidebar-width: 280px;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }
        
        .sidebar-nav {
            padding: 20px 0;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .nav-icon {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            margin: 0;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .content-area {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .stat-change {
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--danger-color);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-card,
        .activity-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 20px;
        }
        
        .alert-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border-right: 4px solid;
        }
        
        .alert-warning {
            border-right-color: var(--warning-color);
        }
        
        .alert-danger {
            border-right-color: var(--danger-color);
        }
        
        .alert-info {
            border-right-color: var(--info-color);
        }
        
        .alert-header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .alert-icon {
            margin-left: 10px;
            width: 20px;
        }
        
        .alert-title {
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .alert-message {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .table-responsive {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 5px 10px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="index.php" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                ExRayan Admin
            </a>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link active">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    لوحة التحكم
                </a>
            </div>
            
            <div class="nav-item">
                <a href="ads" class="nav-link">
                    <i class="fas fa-bullhorn nav-icon"></i>
                    إدارة الإعلانات
                </a>
            </div>
            
            <div class="nav-item">
                <a href="users" class="nav-link">
                    <i class="fas fa-users nav-icon"></i>
                    إدارة المستخدمين
                </a>
            </div>
            
            <div class="nav-item">
                <a href="sections" class="nav-link">
                    <i class="fas fa-folder nav-icon"></i>
                    إدارة الأقسام
                </a>
            </div>
            
            <div class="nav-item">
                <a href="stores" class="nav-link">
                    <i class="fas fa-store nav-icon"></i>
                    إدارة المتاجر
                </a>
            </div>
            
            <div class="nav-item">
                <a href="auctions" class="nav-link">
                    <i class="fas fa-gavel nav-icon"></i>
                    إدارة المزادات
                </a>
            </div>
            
            <div class="nav-item">
                <a href="transactions" class="nav-link">
                    <i class="fas fa-credit-card nav-icon"></i>
                    المعاملات المالية
                </a>
            </div>
            
            <div class="nav-item">
                <a href="reports" class="nav-link">
                    <i class="fas fa-chart-bar nav-icon"></i>
                    التقارير والإحصائيات
                </a>
            </div>
            
            <div class="nav-item">
                <a href="settings" class="nav-link">
                    <i class="fas fa-cog nav-icon"></i>
                    الإعدادات
                </a>
            </div>
            
            <div class="nav-item">
                <a href="security" class="nav-link">
                    <i class="fas fa-shield-alt nav-icon"></i>
                    الأمان والحماية
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt nav-icon"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- شريط التنقل العلوي -->
        <div class="top-navbar">
            <h1 class="page-title"><?= htmlspecialchars($title) ?></h1>
            
            <div class="user-menu">
                <div class="user-avatar">
                    <?= strtoupper(substr($user['username'], 0, 1)) ?>
                </div>
                <div>
                    <div style="font-weight: 600;"><?= htmlspecialchars($user['username']) ?></div>
                    <small class="text-muted"><?= htmlspecialchars($user['classification']) ?></small>
                </div>
            </div>
        </div>
        
        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- التنبيهات -->
            <?php if (!empty($alerts)): ?>
            <div class="mb-4">
                <h5 class="mb-3">التنبيهات والإشعارات</h5>
                <?php foreach ($alerts as $alert): ?>
                <div class="alert-item alert-<?= $alert['type'] ?>">
                    <div class="alert-header">
                        <i class="<?= $alert['icon'] ?> alert-icon"></i>
                        <span class="alert-title"><?= htmlspecialchars($alert['title']) ?></span>
                    </div>
                    <div class="alert-message"><?= htmlspecialchars($alert['message']) ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <!-- بطاقات الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: var(--primary-color);">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="text-end">
                            <div class="stat-number"><?= number_format($stats['total_ads']) ?></div>
                            <div class="stat-label">إجمالي الإعلانات</div>
                        </div>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <?= number_format($stats['active_ads']) ?> نشط
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: var(--success-color);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="text-end">
                            <div class="stat-number"><?= number_format($stats['total_users']) ?></div>
                            <div class="stat-label">إجمالي المستخدمين</div>
                        </div>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <?= number_format($stats['new_users_today']) ?> جديد اليوم
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: var(--warning-color);">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="text-end">
                            <div class="stat-number"><?= number_format($stats['total_stores']) ?></div>
                            <div class="stat-label">إجمالي المتاجر</div>
                        </div>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <?= number_format($stats['active_stores']) ?> نشط
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: var(--info-color);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="text-end">
                            <div class="stat-number"><?= number_format($stats['total_revenue'], 2) ?></div>
                            <div class="stat-label">إجمالي الإيرادات (د.ك)</div>
                        </div>
                    </div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <?= number_format($stats['monthly_revenue'], 2) ?> هذا الشهر
                    </div>
                </div>
            </div>
            
            <!-- الرسوم البيانية والأنشطة -->
            <div class="dashboard-grid">
                <div class="chart-card">
                    <h5 class="card-title">الإحصائيات الشهرية</h5>
                    <canvas id="monthlyChart" height="300"></canvas>
                </div>
                
                <div class="activity-card">
                    <h5 class="card-title">الأنشطة الأخيرة</h5>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach (array_slice($recent_activities, 0, 10) as $activity): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle p-2">
                                    <i class="fas fa-user text-muted"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold"><?= htmlspecialchars($activity['username'] ?? 'مستخدم غير معروف') ?></div>
                                <div class="text-muted small"><?= htmlspecialchars($activity['action']) ?></div>
                                <div class="text-muted small"><?= date('Y-m-d H:i', strtotime($activity['created_at'])) ?></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- أحدث الإعلانات -->
            <div class="table-responsive">
                <h5 class="card-title">أحدث الإعلانات</h5>
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>المستخدم</th>
                            <th>القسم</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($latest_ads, 0, 10) as $ad): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if ($ad['primary_image']): ?>
                                        <img src="../<?= htmlspecialchars($ad['primary_image']) ?>" 
                                             alt="<?= htmlspecialchars($ad['title']) ?>" 
                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <span><?= htmlspecialchars(mb_substr($ad['title'], 0, 30)) ?>...</span>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($ad['username']) ?></td>
                            <td><?= htmlspecialchars($ad['section_name']) ?></td>
                            <td>
                                <?php
                                $statusClass = [
                                    'active' => 'success',
                                    'pending' => 'warning',
                                    'expired' => 'secondary',
                                    'banned' => 'danger'
                                ];
                                $statusText = [
                                    'active' => 'نشط',
                                    'pending' => 'معلق',
                                    'expired' => 'منتهي',
                                    'banned' => 'محظور'
                                ];
                                ?>
                                <span class="badge bg-<?= $statusClass[$ad['status']] ?? 'secondary' ?>">
                                    <?= $statusText[$ad['status']] ?? $ad['status'] ?>
                                </span>
                            </td>
                            <td><?= date('Y-m-d H:i', strtotime($ad['created_at'])) ?></td>
                            <td>
                                <a href="ads/view/<?= $ad['id'] ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="ads/edit/<?= $ad['id'] ?>" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // الرسم البياني الشهري
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyData = <?= json_encode($monthly_stats) ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthlyData.map(item => item.month),
                datasets: [{
                    label: 'الإعلانات',
                    data: monthlyData.map(item => item.ads),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'المستخدمين',
                    data: monthlyData.map(item => item.users),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // تحديث الصفحة كل 5 دقائق
        setTimeout(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
