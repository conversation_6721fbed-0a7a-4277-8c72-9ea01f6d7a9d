# ExRayan - منصة الإعلانات المبوبة المتطورة

## 🌟 نظرة عامة

ExRayan هي منصة إعلانات مبوبة متطورة وثورية مبنية بـ PHP العادي مع قاعدة بيانات MySQL. تتميز المنصة بتصميم متجاوب مع جميع الأجهزة ودعم كامل للغة العربية (RTL) كلغة افتراضية، مع نظام أمان متقدم وخالي من الثغرات الأمنية.

## 🚀 المميزات الرئيسية

### 📱 التصميم والواجهة
- تصميم متجاوب مع جميع الأجهزة (كمبيوتر، تابلت، هاتف)
- دعم كامل للغة العربية (RTL) كلغة افتراضية
- نظام قوالب مرن وقابل للتخصيص
- واجهة مستخدم حديثة وسهلة الاستخدام

### 🔒 الأمان والحماية
- نظام أمان متعدد الطبقات
- حماية من جميع أنواع الهجمات الإلكترونية
- تشفير البيانات الحساسة
- نظام مصادقة ثنائية (2FA)
- سجل شامل للأنشطة والتنبيهات الأمنية

### 🏢 إدارة شاملة (32 إدارة)
1. **إدارة الإعلانات** - نظام متكامل لإدارة الإعلانات
2. **إدارة الأقسام والنماذج الديناميكية** - أقسام مرنة وحقول مخصصة
3. **إدارة التعليقات** - نظام تعليقات تفاعلي
4. **إدارة الرسائل والمحادثات** - دردشة داخلية متقدمة
5. **إدارة المتاجر** - متاجر إلكترونية متكاملة
6. **إدارة الباقات** - نظام اشتراكات مرن
7. **إدارة العضويات والصلاحيات** - تحكم دقيق في الصلاحيات
8. **إدارة المتابعة** - نظام متابعة اجتماعي
9. **إدارة المحفظة المالية** - نظام مالي آمن
10. **إدارة الدفع الإلكتروني** - بوابات دفع متعددة
11. **إدارة العملات** - دعم عملات متعددة
12. **إدارة التقييمات** - نظام تقييم شفاف
13. **إدارة الشكاوى** - نظام دعم فني متقدم
14. **إدارة التنبيهات** - إشعارات ذكية
15. **إدارة المستخدمين** - إدارة شاملة للحسابات
16. **إدارة سجل النشاطات** - مراقبة شاملة
17. **إدارة التقارير والإحصائيات** - تحليلات ذكية
18. **إدارة الخرائط** - تكامل مع الخرائط
19. **إدارة الإعلانات المميزة** - نظام ترويج متقدم
20. **إدارة المدونة** - نظام محتوى احترافي
21. **إدارة SEO** - تحسين محركات البحث
22. **إدارة الذكاء الاصطناعي** - ميزات ذكية متقدمة
23. **إدارة اللغات** - دعم متعدد اللغات
24. **إدارة القوالب** - نظام قوالب مرن
25. **إدارة صفحات النظام** - صفحات ثابتة قابلة للتخصيص
26. **إدارة الحملات الإعلانية** - حملات ذكية
27. **إدارة الشحن والفواتير** - نظام لوجستي متكامل
28. **إدارة القسائم** - نظام خصومات متقدم
29. **إدارة سجل الدخول** - مراقبة أمنية
30. **إدارة المزادات** - نظام مزادات تفاعلي
31. **إدارة العمولة** - نظام عمولات ديناميكي
32. **إدارة الأمان** - حماية شاملة

### 🤖 الذكاء الاصطناعي
- توليد المحتوى تلقائياً
- تحسين SEO ذكياً
- توصيات مخصصة
- كشف المحتوى المخالف
- تحليل البيانات والتنبؤات

## 🛠️ المتطلبات التقنية

### متطلبات الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- SSL Certificate (للإنتاج)

### المتطلبات الاختيارية
- Redis (للتخزين المؤقت)
- Composer (لإدارة المكتبات)
- Node.js (لأدوات التطوير)

## 📦 التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/exrayan/classified-ads.git
cd classified-ads
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p000 -e "CREATE DATABASE exrayan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# استيراد الهيكل
mysql -u root -p000 exrayan < database_schema.sql
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
# تحرير ملف .env وإدخال بيانات قاعدة البيانات
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 .env
```

## ⚙️ الإعداد

### إعدادات قاعدة البيانات
```env
DB_HOST=127.0.0.1
DB_DATABASE=exrayan
DB_USERNAME=root
DB_PASSWORD=000
```

### إعدادات الأمان
```env
APP_KEY=your-32-character-secret-key
SECURITY_2FA_ENABLED=true
SECURITY_MAX_LOGIN_ATTEMPTS=5
```

### إعدادات الدفع
```env
PAYPAL_CLIENT_ID=your_paypal_id
STRIPE_SECRET=your_stripe_secret
MYFATOORAH_API_KEY=your_myfatoorah_key
```

## 🔧 الاستخدام

### إنشاء حساب مدير
```php
// تشغيل هذا الكود مرة واحدة لإنشاء حساب المدير
$db = Database::getInstance();
$password = Security::hashPassword('admin123');
$db->insert("INSERT INTO users (username, email, password, role_id, classification) VALUES (?, ?, ?, 1, 'admin')", 
    ['admin', '<EMAIL>', $password]);
```

### الوصول للوحة التحكم
```
https://yourdomain.com/admin
```

## 📁 هيكل المشروع

```
exrayan/
├── config/                 # ملفات الإعداد
├── includes/               # الملفات الأساسية
├── controllers/            # كنترولرز MVC
├── models/                # نماذج البيانات
├── views/                 # ملفات العرض
├── assets/                # الملفات الثابتة
├── uploads/               # الملفات المرفوعة
├── logs/                  # ملفات السجلات
├── backups/               # النسخ الاحتياطية
├── database_schema.sql    # هيكل قاعدة البيانات
├── index.php             # نقطة البداية
├── .htaccess             # إعدادات Apache
└── README.md             # هذا الملف
```

## 🔐 الأمان

### الميزات الأمنية
- تشفير كلمات المرور بـ Argon2ID
- حماية من SQL Injection
- حماية من XSS
- حماية من CSRF
- تحديد معدل الطلبات (Rate Limiting)
- سجل شامل للأنشطة المشبوهة

### أفضل الممارسات
- تحديث كلمات المرور دورياً
- تفعيل المصادقة الثنائية
- مراقبة السجلات بانتظام
- تحديث النظام دورياً

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🛠️ أدوات التطوير والتشخيص

### 🔧 أدوات التشخيص المتقدمة
- **تشخيص شامل:** `diagnose.php` - فحص شامل لجميع مكونات النظام
- **اختبار AJAX:** `test_ajax.php` - اختبار مفصل لوظائف AJAX
- **فحص حالة النظام:** `status.php` - تقرير JSON للمراقبة
- **فحص التثبيت:** `check_installation.php` - فحص حالة التثبيت
- **اختبار قاعدة البيانات:** `test_connection.php` - اختبار الاتصال
- **إعادة تعيين قاعدة البيانات:** `reset_database.php` - إعادة تعيين آمنة

### 🚀 أدوات التثبيت
- **صفحة الترحيب:** `welcome.php` - صفحة ترحيب ذكية مع فحص النظام
- **التثبيت التفاعلي:** `install.php` - تثبيت محسن مع معالجة أخطاء متقدمة
- **إنشاء مدير سريع:** `create_admin.php` - إنشاء حساب مدير بسهولة

### 📋 الوثائق
- **دليل التثبيت:** `INSTALLATION.md` - دليل مفصل للتثبيت
- **دليل البدء السريع:** `QUICK_START.md` - خطوات سريعة للتشغيل
- **دليل حل المشاكل:** `TROUBLESHOOTING.md` - حلول للمشاكل الشائعة
- **دليل لوحة التحكم:** `ADMIN_GUIDE.md` - دليل شامل للإدارة

---

## 📞 الدعم

- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://exrayan.com
- **التوثيق:** https://docs.exrayan.com
- **أدوات التشخيص:** استخدم الأدوات المدمجة أعلاه

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا المشروع.

---

**ExRayan** - منصة الإعلانات المبوبة الأكثر تطوراً في المنطقة العربية 🚀
