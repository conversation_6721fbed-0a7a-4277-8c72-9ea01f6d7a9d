<?php
/**
 * صفحة تسجيل الدخول للإدارة - ExRayan Platform
 * Admin Login Page
 */

// تحميل الملفات الأساسية
require_once __DIR__ . '/../includes/Database.php';
require_once __DIR__ . '/../includes/Security.php';
require_once __DIR__ . '/../includes/Session.php';
require_once __DIR__ . '/../includes/Validator.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

Session::start();

// إعادة توجيه إذا كان مسجل الدخول مسبقاً
if (Session::isLoggedIn()) {
    $user = Session::getUser();
    if (in_array($user['classification'], ['admin', 'moderator'])) {
        header('Location: index.php');
        exit;
    }
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من CSRF Token
        if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('رمز الأمان غير صحيح');
        }
        
        $username = Security::sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // التحقق من البيانات
        $validator = new Validator($_POST);
        $validator->required('username', 'اسم المستخدم مطلوب')
                 ->required('password', 'كلمة المرور مطلوبة');
        
        if ($validator->fails()) {
            throw new Exception($validator->getFirstError('username') ?: $validator->getFirstError('password'));
        }
        
        // التحقق من معدل المحاولات
        $identifier = Security::getRealIpAddress() . '_' . $username;
        if (!Security::checkRateLimit($identifier, 5, 300)) {
            throw new Exception('تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى بعد 5 دقائق.');
        }
        
        // البحث عن المستخدم
        $db = Database::getInstance();
        $user = $db->selectOne(
            "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );
        
        if (!$user || !Security::verifyPassword($password, $user['password'])) {
            // تسجيل محاولة فاشلة
            Security::logSuspiciousActivity($user['id'] ?? null, 'failed_login', [
                'username' => $username,
                'ip' => Security::getRealIpAddress()
            ]);
            
            throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
        
        // التحقق من صلاحيات الإدارة
        if (!in_array($user['classification'], ['admin', 'moderator'])) {
            Security::logSuspiciousActivity($user['id'], 'unauthorized_admin_access', [
                'username' => $username,
                'classification' => $user['classification']
            ]);

            throw new Exception('ليس لديك صلاحية للوصول لهذه الصفحة. تصنيفك الحالي: ' . $user['classification'] . '. يجب أن يكون admin أو moderator.');
        }
        
        // تسجيل الدخول
        Session::login($user);
        
        // إعادة التوجيه
        $redirect = $_GET['redirect'] ?? 'index.php';
        header('Location: ' . $redirect);
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// توليد CSRF Token
$csrfToken = Security::generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم | ExRayan</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            display: flex;
            min-height: 500px;
        }
        
        .login-form {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-banner {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 40px;
        }
        
        .login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #333;
        }
        
        .login-subtitle {
            color: #666;
            margin-bottom: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            border-radius: 10px;
            margin-bottom: 25px;
        }
        
        .banner-content h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .banner-content p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .features {
            margin-top: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .feature-icon {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
        
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 10px;
            }
            
            .login-banner {
                order: -1;
                min-height: 200px;
            }
            
            .login-form {
                padding: 30px 20px;
            }
            
            .banner-content h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="text-center mb-4">
                <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                <h1 class="login-title">لوحة التحكم</h1>
                <p class="login-subtitle">مرحباً بك في نظام إدارة ExRayan</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user me-1"></i>
                        اسم المستخدم أو البريد الإلكتروني
                    </label>
                    <div class="input-group">
                        <input type="text" name="username" class="form-control" 
                               value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" 
                               placeholder="أدخل اسم المستخدم" required>
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock me-1"></i>
                        كلمة المرور
                    </label>
                    <div class="input-group">
                        <input type="password" name="password" class="form-control" 
                               placeholder="أدخل كلمة المرور" required>
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="text-center mt-4">
                <a href="../" class="text-muted">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للموقع الرئيسي
                </a>

                <br><br>

                <a href="../fix_admin_user.php" class="text-warning">
                    <i class="fas fa-user-shield me-1"></i>
                    مشكلة في الصلاحيات؟ اضغط هنا
                </a>
            </div>
        </div>
        
        <div class="login-banner">
            <div class="banner-content">
                <h2>ExRayan</h2>
                <p>منصة الإعلانات المبوبة الأكثر تطوراً في الكويت</p>
                
                <div class="features">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span>نظام أمان متقدم</span>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <span>تقارير وإحصائيات ذكية</span>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <span>إدارة شاملة ومرنة</span>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <span>متوافق مع جميع الأجهزة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تركيز على حقل اسم المستخدم
            const usernameField = document.querySelector('input[name="username"]');
            if (usernameField) {
                usernameField.focus();
            }
            
            // تأثير hover على الأزرار
            const loginBtn = document.querySelector('.btn-login');
            loginBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            loginBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
