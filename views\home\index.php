<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'ExRayan') ?></title>
    <meta name="description" content="<?= htmlspecialchars($description ?? '') ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            line-height: 1.6;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 2rem;
        }
        
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1rem;
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-search {
            background: var(--primary-color);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-search:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: white;
            margin-top: -50px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            position: relative;
            z-index: 10;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 600;
        }
        
        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .category-card {
            background: white;
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
            text-decoration: none;
            color: inherit;
        }
        
        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .category-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .category-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .category-count {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .ad-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
            margin-bottom: 30px;
        }
        
        .ad-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .ad-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .ad-content {
            padding: 20px;
        }
        
        .ad-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }
        
        .ad-price {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 0.5rem;
        }
        
        .ad-location {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .featured-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--warning-color);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
            margin-top: 80px;
        }
        
        .footer-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .footer-link {
            color: #adb5bd;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-link:hover {
            color: white;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .search-box {
                padding: 15px;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-store me-2"></i>
                ExRayan
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ads">الإعلانات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores">المتاجر</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/auctions">المزادات</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?= htmlspecialchars($user['username']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/profile">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/my-ads">إعلاناتي</a></li>
                                <li><a class="dropdown-item" href="/wallet">المحفظة</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register">إنشاء حساب</a>
                        </li>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link btn btn-warning text-dark ms-2" href="/post-ad">
                            <i class="fas fa-plus me-1"></i>
                            أضف إعلان
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">مرحباً بك في ExRayan</h1>
            <p class="hero-subtitle">منصة الإعلانات المبوبة الأكثر تطوراً في الكويت</p>
            
            <!-- صندوق البحث -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-box">
                        <form action="/search" method="GET">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <input type="text" name="q" class="form-control search-input" placeholder="ابحث عن أي شيء...">
                                </div>
                                <div class="col-md-4">
                                    <select name="section" class="form-select search-input">
                                        <option value="">جميع الأقسام</option>
                                        <?php foreach ($sections as $section): ?>
                                            <option value="<?= $section['id'] ?>"><?= htmlspecialchars($section['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-search btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- إحصائيات الموقع -->
    <div class="container">
        <div class="stats-section">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['active_ads']) ?></span>
                        <div class="stat-label">إعلان نشط</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['users']) ?></span>
                        <div class="stat-label">عضو مسجل</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['stores']) ?></span>
                        <div class="stat-label">متجر نشط</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['auctions']) ?></span>
                        <div class="stat-label">مزاد جاري</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأقسام الرئيسية -->
    <section class="py-5">
        <div class="container">
            <h2 class="section-title">تصفح الأقسام</h2>
            <div class="row">
                <?php foreach ($sections as $section): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <a href="/section/<?= $section['slug'] ?>" class="category-card d-block">
                            <?php if ($section['icon']): ?>
                                <i class="<?= htmlspecialchars($section['icon']) ?> category-icon"></i>
                            <?php else: ?>
                                <i class="fas fa-folder category-icon"></i>
                            <?php endif; ?>
                            <div class="category-name"><?= htmlspecialchars($section['name']) ?></div>
                            <div class="category-count"><?= number_format($section['ads_count']) ?> إعلان</div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- الإعلانات المميزة -->
    <?php if (!empty($featured_ads)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title">الإعلانات المميزة</h2>
            <div class="row">
                <?php foreach ($featured_ads as $ad): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="ad-card position-relative">
                            <span class="featured-badge">مميز</span>
                            <?php if ($ad['primary_image']): ?>
                                <img src="/<?= htmlspecialchars($ad['primary_image']) ?>" alt="<?= htmlspecialchars($ad['title']) ?>" class="ad-image">
                            <?php else: ?>
                                <div class="ad-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            <div class="ad-content">
                                <h5 class="ad-title"><?= htmlspecialchars(mb_substr($ad['title'], 0, 50)) ?>...</h5>
                                <?php if ($ad['price']): ?>
                                    <div class="ad-price"><?= number_format($ad['price']) ?> د.ك</div>
                                <?php endif; ?>
                                <div class="ad-location">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?= htmlspecialchars($ad['city_name'] ?? 'غير محدد') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- أحدث الإعلانات -->
    <section class="py-5">
        <div class="container">
            <h2 class="section-title">أحدث الإعلانات</h2>
            <div class="row">
                <?php foreach ($latest_ads as $ad): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="ad-card">
                            <?php if ($ad['primary_image']): ?>
                                <img src="/<?= htmlspecialchars($ad['primary_image']) ?>" alt="<?= htmlspecialchars($ad['title']) ?>" class="ad-image">
                            <?php else: ?>
                                <div class="ad-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            <div class="ad-content">
                                <h5 class="ad-title"><?= htmlspecialchars(mb_substr($ad['title'], 0, 50)) ?>...</h5>
                                <?php if ($ad['price']): ?>
                                    <div class="ad-price"><?= number_format($ad['price']) ?> د.ك</div>
                                <?php endif; ?>
                                <div class="ad-location">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?= htmlspecialchars($ad['city_name'] ?? 'غير محدد') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="/ads" class="btn btn-primary btn-lg">
                    عرض جميع الإعلانات
                    <i class="fas fa-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="footer-title">ExRayan</h5>
                    <p class="text-muted">منصة الإعلانات المبوبة الأكثر تطوراً في الكويت. نوفر لك تجربة فريدة للبيع والشراء بأمان وسهولة.</p>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="footer-title">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about" class="footer-link">من نحن</a></li>
                        <li><a href="/contact" class="footer-link">اتصل بنا</a></li>
                        <li><a href="/help" class="footer-link">المساعدة</a></li>
                        <li><a href="/blog" class="footer-link">المدونة</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-title">الشروط والأحكام</h5>
                    <ul class="list-unstyled">
                        <li><a href="/terms" class="footer-link">شروط الاستخدام</a></li>
                        <li><a href="/privacy" class="footer-link">سياسة الخصوصية</a></li>
                        <li><a href="/cookies" class="footer-link">سياسة الكوكيز</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-title">تابعنا</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="footer-link"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="footer-link"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="footer-link"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="footer-link"><i class="fab fa-linkedin fa-2x"></i></a>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; 2024 ExRayan. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">صنع بـ <i class="fas fa-heart text-danger"></i> في الكويت</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.category-card, .ad-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // تحريك الأرقام في الإحصائيات
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalNumber = parseInt(stat.textContent.replace(/,/g, ''));
                let currentNumber = 0;
                const increment = finalNumber / 50;
                
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        currentNumber = finalNumber;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(currentNumber).toLocaleString();
                }, 30);
            });
        });
    </script>
</body>
</html>
